/**
 * Warning: This set of exports is purely intended to be used by other MSAL libraries, and should be considered potentially unstable. We strongly discourage using them directly, you do so at your own risk.
 * Breaking changes to these APIs will be shipped under a minor version, instead of a major version.
 */
export { Serializer } from "./cache/serializer/Serializer.js";
export { Deserializer } from "./cache/serializer/Deserializer.js";
//# sourceMappingURL=internals.d.ts.map