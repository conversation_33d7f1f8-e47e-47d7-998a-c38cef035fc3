{"version": 3, "file": "fetchHttpClient.js", "sourceRoot": "", "sources": ["../../src/fetchHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAQ9D,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,MAAM,sBAAsB,CAAC;AAEjF;;GAEG;AACH,SAAS,MAAM,CAAC,IAAa;IAC3B,gFAAgF;IAChF,OAAO,CAAC,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,IAAI,IAAI,YAAY,IAAI,CAAC;AAC1F,CAAC;AAED;;;GAGG;AACH,MAAM,eAAe;IACnB;;;OAGG;IACI,KAAK,CAAC,WAAW,CAAC,OAAwB;QAC/C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;QAE7C,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,qBAAqB,OAAO,CAAC,GAAG,0CAA0C,CAAC,CAAC;QAC9F,CAAC;QAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,WAAW,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,MAAM,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;CACF;AAED;;GAEG;AACH,KAAK,UAAU,WAAW,CAAC,OAAwB;IACjD,MAAM,EAAE,eAAe,EAAE,sBAAsB,EAAE,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC9E,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACnD,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACnE,MAAM,WAAW,iCACf,IAAI,EAAE,WAAW,EACjB,MAAM,EAAE,OAAO,CAAC,MAAM,EACtB,OAAO,EAAE,OAAO,EAChB,MAAM,EAAE,eAAe,CAAC,MAAM,IAI3B,CAAC,aAAa,IAAI,OAAO,CAAC,SAAS;YACpC,CAAC,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,EAAE;YACtE,CAAC,CAAC,EAAE,CAAC,GACJ,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAC/D,CAAC;QAEF,4DAA4D;QAC5D,gEAAgE;QAChE,4CAA4C;QAC5C,IAAI,SAAS,EAAE,CAAC;YACb,WAAmB,CAAC,MAAM,GAAG,MAAM,CAAC;QACvC,CAAC;QACD;;;;;WAKG;QACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,GAAG,kCACnC,WAAW,GACX,OAAO,CAAC,gBAAgB,EAC3B,CAAC;QACH,yEAAyE;QACzE,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACrD,OAAO,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC/D,CAAC;QACD,OAAO,qBAAqB,CAAC,QAAQ,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;IAC1E,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,sBAAsB,aAAtB,sBAAsB,uBAAtB,sBAAsB,EAAI,CAAC;QAC3B,MAAM,CAAC,CAAC;IACV,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAClC,YAAsB,EACtB,OAAwB,EACxB,sBAAmC;;IAEnC,MAAM,OAAO,GAAG,oBAAoB,CAAC,YAAY,CAAC,CAAC;IACnD,MAAM,QAAQ,GAAqB;QACjC,OAAO;QACP,OAAO;QACP,MAAM,EAAE,YAAY,CAAC,MAAM;KAC5B,CAAC;IAEF,MAAM,UAAU,GAAG,mBAAmB,CAAC,YAAY,CAAC,IAAI,CAAC;QACvD,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI,EAAE;YACjC,UAAU,EAAE,OAAO,CAAC,kBAAkB;YACtC,KAAK,EAAE,sBAAsB;SAC9B,CAAC;QACJ,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC;IAEtB;IACE,2FAA2F;IAC3F,CAAA,MAAA,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,MAAM,CAAC,iBAAiB,CAAC;SAChE,MAAA,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,EACvD,CAAC;QACD,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC;YACjC,QAAQ,CAAC,iBAAiB,GAAG,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,SAAS,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,MAAM,cAAc,GAAG,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC;YAChD,QAAQ,CAAC,QAAQ,GAAG,cAAc,CAAC,IAAI,EAAE,CAAC;YAC1C,sBAAsB,aAAtB,sBAAsB,uBAAtB,sBAAsB,EAAI,CAAC;QAC7B,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,cAAc,GAAG,IAAI,QAAQ,CAAC,UAAU,CAAC,CAAC;QAEhD,QAAQ,CAAC,UAAU,GAAG,MAAM,cAAc,CAAC,IAAI,EAAE,CAAC;QAClD,sBAAsB,aAAtB,sBAAsB,uBAAtB,sBAAsB,EAAI,CAAC;IAC7B,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAwB;IAIhD,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;IAE9C,mBAAmB;IACnB,IAAI,sBAAgD,CAAC;IAErD;;OAEG;IACH,IAAI,aAAiD,CAAC;IACtD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QACxB,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,IAAI,UAAU,CAAC,+DAA+D,CAAC,CAAC;QACxF,CAAC;QAED,aAAa,GAAG,CAAC,KAAY,EAAE,EAAE;YAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC3B,eAAe,CAAC,KAAK,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC;QACF,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAC7D,sBAAsB,GAAG,GAAG,EAAE;;YAC5B,IAAI,aAAa,EAAE,CAAC;gBAClB,MAAA,OAAO,CAAC,WAAW,0CAAE,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YACnE,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAED,uEAAuE;IACvE,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;QACxB,UAAU,CAAC,GAAG,EAAE;YACd,eAAe,CAAC,KAAK,EAAE,CAAC;QAC1B,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IACtB,CAAC;IAED,OAAO,EAAE,eAAe,EAAE,sBAAsB,EAAE,CAAC;AACrD,CAAC;AAED;;GAEG;AACH,wEAAwE;AACxE,SAAS,QAAQ,CAAC,CAAY,EAAE,OAAwB;;IACtD,IAAI,CAAC,IAAI,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,MAAK,YAAY,EAAE,CAAC;QAClC,OAAO,CAAC,CAAC;IACX,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,SAAS,CAAC,0BAA0B,CAAC,CAAC,OAAO,EAAE,EAAE;YAC1D,IAAI,EAAE,MAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,mCAAI,SAAS,CAAC,kBAAkB;YAC7C,OAAO;SACR,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,eAAgC;IACzD,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;IAC9B,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,eAAe,EAAE,CAAC;QAC5C,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,oBAAoB,CAAC,YAAsB;IAClD,MAAM,eAAe,GAAG,iBAAiB,EAAE,CAAC;IAC5C,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;QACjD,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAeD,SAAS,gBAAgB,CAAC,OAAwB;IAChD,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;IAChF,IAAI,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;IAC5E,CAAC;IAED,OAAO,mBAAmB,CAAC,IAAI,CAAC;QAC9B,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,OAAO,CAAC,gBAAgB,EAAE,CAAC,EAAE;QAC5F,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AACjC,CAAC;AAED;;;;;GAKG;AACH,SAAS,eAAe,CACtB,cAA0C,EAC1C,UAA0F,EAAE;IAE5F,IAAI,WAAW,GAAG,CAAC,CAAC;IACpB,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IAEtC,uEAAuE;IACvE,qBAAqB;IACrB,IAAI,0BAA0B,CAAC,cAAc,CAAC,EAAE,CAAC;QAC/C,OAAO,cAAc,CAAC,WAAW,CAC/B,IAAI,eAAe,CAAC;YAClB,SAAS,CAAC,KAAK,EAAE,UAAU;gBACzB,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;oBACnB,UAAU,CAAC,SAAS,EAAE,CAAC;oBACvB,OAAO;gBACT,CAAC;gBAED,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC1B,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC;gBAC5B,IAAI,UAAU,EAAE,CAAC;oBACf,UAAU,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;YACD,KAAK;gBACH,KAAK,aAAL,KAAK,uBAAL,KAAK,EAAI,CAAC;YACZ,CAAC;SACF,CAAC,CACH,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,uFAAuF;QACvF,0DAA0D;QAC1D,MAAM,MAAM,GAAG,cAAc,CAAC,SAAS,EAAE,CAAC;QAC1C,OAAO,IAAI,cAAc,CAAC;YACxB,KAAK,CAAC,IAAI,CAAC,UAAU;;gBACnB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC5C,4DAA4D;gBAC5D,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBACnB,KAAK,aAAL,KAAK,uBAAL,KAAK,EAAI,CAAC;oBACV,mBAAmB;oBACnB,UAAU,CAAC,KAAK,EAAE,CAAC;oBACnB,MAAM,CAAC,WAAW,EAAE,CAAC;oBACrB,OAAO;gBACT,CAAC;gBAED,WAAW,IAAI,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,mCAAI,CAAC,CAAC;gBAElC,qDAAqD;gBACrD,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAE1B,IAAI,UAAU,EAAE,CAAC;oBACf,UAAU,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC;YACD,MAAM,CAAC,MAAe;gBACpB,KAAK,aAAL,KAAK,uBAAL,KAAK,EAAI,CAAC;gBACV,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC/B,CAAC;SACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,qBAAqB;IACnC,OAAO,IAAI,eAAe,EAAE,CAAC;AAC/B,CAAC;AAED,SAAS,0BAA0B,CAAC,cAA8B;IAChE,OAAO,cAAc,CAAC,WAAW,KAAK,SAAS,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,CAAC;AACxF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { AbortError } from \"./abort-controller/AbortError.js\";\nimport type {\n  HttpClient,\n  HttpHeaders as PipelineHeaders,\n  PipelineRequest,\n  PipelineResponse,\n  TransferProgressEvent,\n} from \"./interfaces.js\";\nimport { RestError } from \"./restError.js\";\nimport { createHttpHeaders } from \"./httpHeaders.js\";\nimport { isNodeReadableStream, isWebReadableStream } from \"./util/typeGuards.js\";\n\n/**\n * Checks if the body is a Blob or Blob-like\n */\nfunction isBlob(body: unknown): body is Blob {\n  // File objects count as a type of Blob, so we want to use instanceof explicitly\n  return (typeof Blob === \"function\" || typeof Blob === \"object\") && body instanceof Blob;\n}\n\n/**\n * A HttpClient implementation that uses window.fetch to send HTTP requests.\n * @internal\n */\nclass FetchHttpClient implements HttpClient {\n  /**\n   * Makes a request over an underlying transport layer and returns the response.\n   * @param request - The request to be made.\n   */\n  public async sendRequest(request: PipelineRequest): Promise<PipelineResponse> {\n    const url = new URL(request.url);\n    const isInsecure = url.protocol !== \"https:\";\n\n    if (isInsecure && !request.allowInsecureConnection) {\n      throw new Error(`Cannot connect to ${request.url} while allowInsecureConnection is false.`);\n    }\n\n    if (request.proxySettings) {\n      throw new Error(\"HTTP proxy is not supported in browser environment\");\n    }\n\n    try {\n      return await makeRequest(request);\n    } catch (e: any) {\n      throw getError(e, request);\n    }\n  }\n}\n\n/**\n * Sends a request\n */\nasync function makeRequest(request: PipelineRequest): Promise<PipelineResponse> {\n  const { abortController, abortControllerCleanup } = setupAbortSignal(request);\n  try {\n    const headers = buildFetchHeaders(request.headers);\n    const { streaming, body: requestBody } = buildRequestBody(request);\n    const requestInit: RequestInit = {\n      body: requestBody,\n      method: request.method,\n      headers: headers,\n      signal: abortController.signal,\n      // Cloudflare doesn't implement the full Fetch API spec\n      // because of some of it doesn't make sense in the edge.\n      // See https://github.com/cloudflare/workerd/issues/902\n      ...(\"credentials\" in Request.prototype\n        ? { credentials: request.withCredentials ? \"include\" : \"same-origin\" }\n        : {}),\n      ...(\"cache\" in Request.prototype ? { cache: \"no-store\" } : {}),\n    };\n\n    // According to https://fetch.spec.whatwg.org/#fetch-method,\n    // init.duplex must be set when body is a ReadableStream object.\n    // currently \"half\" is the only valid value.\n    if (streaming) {\n      (requestInit as any).duplex = \"half\";\n    }\n    /**\n     * Developers of the future:\n     * Do not set redirect: \"manual\" as part\n     * of request options.\n     * It will not work as you expect.\n     */\n    const response = await fetch(request.url, {\n      ...requestInit,\n      ...request.requestOverrides,\n    });\n    // If we're uploading a blob, we need to fire the progress event manually\n    if (isBlob(request.body) && request.onUploadProgress) {\n      request.onUploadProgress({ loadedBytes: request.body.size });\n    }\n    return buildPipelineResponse(response, request, abortControllerCleanup);\n  } catch (e) {\n    abortControllerCleanup?.();\n    throw e;\n  }\n}\n\n/**\n * Creates a pipeline response from a Fetch response;\n */\nasync function buildPipelineResponse(\n  httpResponse: Response,\n  request: PipelineRequest,\n  abortControllerCleanup?: () => void,\n): Promise<PipelineResponse> {\n  const headers = buildPipelineHeaders(httpResponse);\n  const response: PipelineResponse = {\n    request,\n    headers,\n    status: httpResponse.status,\n  };\n\n  const bodyStream = isWebReadableStream(httpResponse.body)\n    ? buildBodyStream(httpResponse.body, {\n        onProgress: request.onDownloadProgress,\n        onEnd: abortControllerCleanup,\n      })\n    : httpResponse.body;\n\n  if (\n    // Value of POSITIVE_INFINITY in streamResponseStatusCodes is considered as any status code\n    request.streamResponseStatusCodes?.has(Number.POSITIVE_INFINITY) ||\n    request.streamResponseStatusCodes?.has(response.status)\n  ) {\n    if (request.enableBrowserStreams) {\n      response.browserStreamBody = bodyStream ?? undefined;\n    } else {\n      const responseStream = new Response(bodyStream);\n      response.blobBody = responseStream.blob();\n      abortControllerCleanup?.();\n    }\n  } else {\n    const responseStream = new Response(bodyStream);\n\n    response.bodyAsText = await responseStream.text();\n    abortControllerCleanup?.();\n  }\n\n  return response;\n}\n\nfunction setupAbortSignal(request: PipelineRequest): {\n  abortController: AbortController;\n  abortControllerCleanup: (() => void) | undefined;\n} {\n  const abortController = new AbortController();\n\n  // Cleanup function\n  let abortControllerCleanup: (() => void) | undefined;\n\n  /**\n   * Attach an abort listener to the request\n   */\n  let abortListener: ((event: any) => void) | undefined;\n  if (request.abortSignal) {\n    if (request.abortSignal.aborted) {\n      throw new AbortError(\"The operation was aborted. Request has already been canceled.\");\n    }\n\n    abortListener = (event: Event) => {\n      if (event.type === \"abort\") {\n        abortController.abort();\n      }\n    };\n    request.abortSignal.addEventListener(\"abort\", abortListener);\n    abortControllerCleanup = () => {\n      if (abortListener) {\n        request.abortSignal?.removeEventListener(\"abort\", abortListener);\n      }\n    };\n  }\n\n  // If a timeout was passed, call the abort signal once the time elapses\n  if (request.timeout > 0) {\n    setTimeout(() => {\n      abortController.abort();\n    }, request.timeout);\n  }\n\n  return { abortController, abortControllerCleanup };\n}\n\n/**\n * Gets the specific error\n */\n// eslint-disable-next-line @azure/azure-sdk/ts-use-interface-parameters\nfunction getError(e: RestError, request: PipelineRequest): RestError {\n  if (e && e?.name === \"AbortError\") {\n    return e;\n  } else {\n    return new RestError(`Error sending request: ${e.message}`, {\n      code: e?.code ?? RestError.REQUEST_SEND_ERROR,\n      request,\n    });\n  }\n}\n\n/**\n * Converts PipelineRequest headers to Fetch headers\n */\nfunction buildFetchHeaders(pipelineHeaders: PipelineHeaders): Headers {\n  const headers = new Headers();\n  for (const [name, value] of pipelineHeaders) {\n    headers.append(name, value);\n  }\n\n  return headers;\n}\n\nfunction buildPipelineHeaders(httpResponse: Response): PipelineHeaders {\n  const responseHeaders = createHttpHeaders();\n  for (const [name, value] of httpResponse.headers) {\n    responseHeaders.set(name, value);\n  }\n\n  return responseHeaders;\n}\n\ninterface BuildRequestBodyResponse {\n  body:\n    | string\n    | Blob\n    | ReadableStream<Uint8Array>\n    | ArrayBuffer\n    | ArrayBufferView\n    | FormData\n    | null\n    | undefined;\n  streaming: boolean;\n}\n\nfunction buildRequestBody(request: PipelineRequest): BuildRequestBodyResponse {\n  const body = typeof request.body === \"function\" ? request.body() : request.body;\n  if (isNodeReadableStream(body)) {\n    throw new Error(\"Node streams are not supported in browser environment.\");\n  }\n\n  return isWebReadableStream(body)\n    ? { streaming: true, body: buildBodyStream(body, { onProgress: request.onUploadProgress }) }\n    : { streaming: false, body };\n}\n\n/**\n * Reads the request/response original stream and stream it through a new\n * ReadableStream, this is done to be able to report progress in a way that\n * all modern browsers support. TransformStreams would be an alternative,\n * however they are not yet supported by all browsers i.e Firefox\n */\nfunction buildBodyStream(\n  readableStream: ReadableStream<Uint8Array>,\n  options: { onProgress?: (progress: TransferProgressEvent) => void; onEnd?: () => void } = {},\n): ReadableStream<Uint8Array> {\n  let loadedBytes = 0;\n  const { onProgress, onEnd } = options;\n\n  // If the current browser supports pipeThrough we use a TransformStream\n  // to report progress\n  if (isTransformStreamSupported(readableStream)) {\n    return readableStream.pipeThrough(\n      new TransformStream({\n        transform(chunk, controller) {\n          if (chunk === null) {\n            controller.terminate();\n            return;\n          }\n\n          controller.enqueue(chunk);\n          loadedBytes += chunk.length;\n          if (onProgress) {\n            onProgress({ loadedBytes });\n          }\n        },\n        flush() {\n          onEnd?.();\n        },\n      }),\n    );\n  } else {\n    // If we can't use transform streams, wrap the original stream in a new readable stream\n    // and use pull to enqueue each chunk and report progress.\n    const reader = readableStream.getReader();\n    return new ReadableStream({\n      async pull(controller) {\n        const { done, value } = await reader.read();\n        // When no more data needs to be consumed, break the reading\n        if (done || !value) {\n          onEnd?.();\n          // Close the stream\n          controller.close();\n          reader.releaseLock();\n          return;\n        }\n\n        loadedBytes += value?.length ?? 0;\n\n        // Enqueue the next data chunk into our target stream\n        controller.enqueue(value);\n\n        if (onProgress) {\n          onProgress({ loadedBytes });\n        }\n      },\n      cancel(reason?: string) {\n        onEnd?.();\n        return reader.cancel(reason);\n      },\n    });\n  }\n}\n\n/**\n * Create a new HttpClient instance for the browser environment.\n * @internal\n */\nexport function createFetchHttpClient(): HttpClient {\n  return new FetchHttpClient();\n}\n\nfunction isTransformStreamSupported(readableStream: ReadableStream): boolean {\n  return readableStream.pipeThrough !== undefined && self.TransformStream !== undefined;\n}\n"]}