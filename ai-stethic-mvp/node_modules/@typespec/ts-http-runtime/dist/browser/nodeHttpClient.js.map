{"version": 3, "file": "nodeHttpClient.js", "sourceRoot": "", "sources": ["../../src/nodeHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAC;AACpC,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,UAAU,EAAE,MAAM,kCAAkC,CAAC;AAU9D,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAE3C,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAEhD,MAAM,oBAAoB,GAAG,EAAE,CAAC;AAEhC,SAAS,gBAAgB,CAAC,IAAS;IACjC,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC;AACjD,CAAC;AAED,SAAS,gBAAgB,CAAC,MAA6B;IACrD,IAAI,MAAM,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;QAC9B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;QAC7B,MAAM,OAAO,GAAG,GAAS,EAAE;YACzB,OAAO,EAAE,CAAC;YACV,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxC,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACtC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC;QAEF,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC5B,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC1B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,aAAa,CAAC,IAAS;IAC9B,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,CAAC;AACrD,CAAC;AAED,MAAM,eAAgB,SAAQ,SAAS;IAIrC,sEAAsE;IACtE,UAAU,CAAC,KAAsB,EAAE,SAAiB,EAAE,QAAkB;QACtE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjB,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC;QACjC,IAAI,CAAC;YACH,IAAI,CAAC,gBAAgB,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;YACzD,QAAQ,EAAE,CAAC;QACb,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,QAAQ,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;IACH,CAAC;IAED,YAAY,gBAA2D;QACrE,KAAK,EAAE,CAAC;QAhBF,gBAAW,GAAG,CAAC,CAAC;QAiBtB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC3C,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,cAAc;IAApB;QAEU,sBAAiB,GAAsC,IAAI,OAAO,EAAE,CAAC;IA2O/E,CAAC;IAzOC;;;OAGG;IACI,KAAK,CAAC,WAAW,CAAC,OAAwB;;QAC/C,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAC9C,IAAI,aAAiD,CAAC;QACtD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBAChC,MAAM,IAAI,UAAU,CAAC,+DAA+D,CAAC,CAAC;YACxF,CAAC;YAED,aAAa,GAAG,CAAC,KAAY,EAAE,EAAE;gBAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC3B,eAAe,CAAC,KAAK,EAAE,CAAC;gBAC1B,CAAC;YACH,CAAC,CAAC;YACF,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAC/D,CAAC;QAED,IAAI,SAAoD,CAAC;QACzD,IAAI,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;YACxB,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC1B,MAAM,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;gBAClC,MAAM,CAAC,IAAI,CAAC,eAAe,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;gBAC1F,eAAe,CAAC,KAAK,EAAE,CAAC;YAC1B,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;QAED,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC9D,MAAM,gBAAgB,GACpB,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,QAAQ,CAAC,MAAM,CAAC,MAAI,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,QAAQ,CAAC,SAAS,CAAC,CAAA,CAAC;QAE1E,IAAI,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;QAC9E,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;YACvC,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;gBACxB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;YACpD,CAAC;QACH,CAAC;QAED,IAAI,cAAiD,CAAC;QACtD,IAAI,CAAC;YACH,IAAI,IAAI,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBACrC,MAAM,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;gBAClD,MAAM,kBAAkB,GAAG,IAAI,eAAe,CAAC,gBAAgB,CAAC,CAAC;gBACjE,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;oBACnC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;gBACH,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBAChC,CAAC;qBAAM,CAAC;oBACN,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC;gBAED,IAAI,GAAG,kBAAkB,CAAC;YAC5B,CAAC;YAED,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;YAEnE,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5B,YAAY,CAAC,SAAS,CAAC,CAAC;YAC1B,CAAC;YAED,MAAM,OAAO,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;YAExC,MAAM,MAAM,GAAG,MAAA,GAAG,CAAC,UAAU,mCAAI,CAAC,CAAC;YACnC,MAAM,QAAQ,GAAqB;gBACjC,MAAM;gBACN,OAAO;gBACP,OAAO;aACR,CAAC;YAEF,0CAA0C;YAC1C,uDAAuD;YACvD,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBAC9B,8DAA8D;gBAC9D,wBAAwB;gBACxB,GAAG,CAAC,MAAM,EAAE,CAAC;gBACb,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,cAAc,GAAG,gBAAgB,CAAC,CAAC,CAAC,wBAAwB,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YAEjF,MAAM,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;YACtD,IAAI,kBAAkB,EAAE,CAAC;gBACvB,MAAM,oBAAoB,GAAG,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAAC;gBACrE,oBAAoB,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;oBACrC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,CAAC,CAAC,CAAC;gBAChD,CAAC,CAAC,CAAC;gBACH,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBAC1C,cAAc,GAAG,oBAAoB,CAAC;YACxC,CAAC;YAED;YACE,2FAA2F;YAC3F,CAAA,MAAA,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,MAAM,CAAC,iBAAiB,CAAC;iBAChE,MAAA,OAAO,CAAC,yBAAyB,0CAAE,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,EACvD,CAAC;gBACD,QAAQ,CAAC,kBAAkB,GAAG,cAAc,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,UAAU,GAAG,MAAM,YAAY,CAAC,cAAc,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;gBAAS,CAAC;YACT,0BAA0B;YAC1B,IAAI,OAAO,CAAC,WAAW,IAAI,aAAa,EAAE,CAAC;gBACzC,IAAI,gBAAgB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBACzC,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3B,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBAC5C,CAAC;gBACD,IAAI,kBAAkB,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC3C,IAAI,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC;oBACrC,kBAAkB,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;gBACxD,CAAC;gBACD,OAAO,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC;qBAChD,IAAI,CAAC,GAAG,EAAE;;oBACT,iDAAiD;oBACjD,IAAI,aAAa,EAAE,CAAC;wBAClB,MAAA,OAAO,CAAC,WAAW,0CAAE,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;oBACnE,CAAC;gBACH,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;oBACX,MAAM,CAAC,OAAO,CAAC,qDAAqD,EAAE,CAAC,CAAC,CAAC;gBAC3E,CAAC,CAAC,CAAC;YACP,CAAC;QACH,CAAC;IACH,CAAC;IAEO,WAAW,CACjB,OAAwB,EACxB,eAAgC,EAChC,IAAsB;;QAEtB,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEjC,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;QAE7C,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACnD,MAAM,IAAI,KAAK,CAAC,qBAAqB,OAAO,CAAC,GAAG,0CAA0C,CAAC,CAAC;QAC9F,CAAC;QAED,MAAM,KAAK,GAAG,MAAC,OAAO,CAAC,KAAoB,mCAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC1F,MAAM,OAAO,mBACX,KAAK,EACL,QAAQ,EAAE,GAAG,CAAC,QAAQ,EACtB,IAAI,EAAE,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,EAAE,EACpC,IAAI,EAAE,GAAG,CAAC,IAAI,EACd,MAAM,EAAE,OAAO,CAAC,MAAM,EACtB,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,IACpD,OAAO,CAAC,gBAAgB,CAC5B,CAAC;QAEF,OAAO,IAAI,OAAO,CAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3D,MAAM,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE1F,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,GAA8B,EAAE,EAAE;;gBACnD,MAAM,CACJ,IAAI,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,MAAA,GAAG,CAAC,IAAI,mCAAI,SAAS,CAAC,kBAAkB,EAAE,OAAO,EAAE,CAAC,CACxF,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;gBACpD,MAAM,UAAU,GAAG,IAAI,UAAU,CAC/B,uFAAuF,CACxF,CAAC;gBACF,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACxB,MAAM,CAAC,UAAU,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;YACH,IAAI,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjB,CAAC;iBAAM,IAAI,IAAI,EAAE,CAAC;gBAChB,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBACtD,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChB,CAAC;qBAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/B,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBACnF,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,IAAI,CAAC,CAAC;oBAC7C,MAAM,CAAC,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,sDAAsD;gBACtD,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CAAC,OAAwB,EAAE,UAAmB;;QACpE,MAAM,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QAElD,iCAAiC;QACjC,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,gBAAgB,EAAE,CAAC;gBACrB,iEAAiE;gBACjE,OAAO,IAAI,CAAC,WAAW,CAAC;YAC1B,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBAC1B,6DAA6D;gBAC7D,IAAI,CAAC,eAAe,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC7D,CAAC;YACD,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,IAAI,gBAAgB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBAC7C,uDAAuD;gBACvD,+BAA+B;gBAC/B,OAAO,KAAK,CAAC,WAAW,CAAC;YAC3B,CAAC;YAED,iDAAiD;YACjD,MAAM,WAAW,GAAG,MAAA,OAAO,CAAC,WAAW,mCAAI,oBAAoB,CAAC;YAEhE,oDAAoD;YACpD,gDAAgD;YAChD,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAEpD,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,KAAK,CAAC,gBAAgB,EAAE,CAAC;gBAC3D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YAC/D,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK;gBACrB,kDAAkD;gBAClD,SAAS,EAAE,CAAC,gBAAgB,IAEzB,WAAW,EACd,CAAC;YAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAED,SAAS,kBAAkB,CAAC,GAAoB;IAC9C,MAAM,OAAO,GAAG,iBAAiB,EAAE,CAAC;IACpC,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAClC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;aAAM,IAAI,KAAK,EAAE,CAAC;YACjB,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,wBAAwB,CAC/B,MAAuB,EACvB,OAAoB;IAEpB,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IACxD,IAAI,eAAe,KAAK,MAAM,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;SAAM,IAAI,eAAe,KAAK,SAAS,EAAE,CAAC;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACrC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrB,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,YAAY,CAAC,MAA6B;IACjD,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC7C,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,EAAE;YAC1B,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAClC,CAAC;QACH,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACpB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;QACH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YACvB,IAAI,CAAC,IAAI,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,MAAK,YAAY,EAAE,CAAC;gBAClC,MAAM,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACN,MAAM,CACJ,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC,OAAO,EAAE,EAAE;oBAC5D,IAAI,EAAE,SAAS,CAAC,WAAW;iBAC5B,CAAC,CACH,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,aAAa,CAAC,IAAqB;IACjD,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,CAAC,CAAC;IACX,CAAC;SAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;SAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;SAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QACpC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;IAClC,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,oBAAoB;IAClC,OAAO,IAAI,cAAc,EAAE,CAAC;AAC9B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as http from \"node:http\";\nimport * as https from \"node:https\";\nimport * as zlib from \"node:zlib\";\nimport { Transform } from \"node:stream\";\nimport { AbortError } from \"./abort-controller/AbortError.js\";\nimport type {\n  HttpClient,\n  HttpHeaders,\n  PipelineRequest,\n  PipelineResponse,\n  RequestBodyType,\n  TlsSettings,\n  TransferProgressEvent,\n} from \"./interfaces.js\";\nimport { createHttpHeaders } from \"./httpHeaders.js\";\nimport { RestError } from \"./restError.js\";\nimport type { IncomingMessage } from \"node:http\";\nimport { logger } from \"./log.js\";\nimport { Sanitizer } from \"./util/sanitizer.js\";\n\nconst DEFAULT_TLS_SETTINGS = {};\n\nfunction isReadableStream(body: any): body is NodeJS.ReadableStream {\n  return body && typeof body.pipe === \"function\";\n}\n\nfunction isStreamComplete(stream: NodeJS.ReadableStream): Promise<void> {\n  if (stream.readable === false) {\n    return Promise.resolve();\n  }\n\n  return new Promise((resolve) => {\n    const handler = (): void => {\n      resolve();\n      stream.removeListener(\"close\", handler);\n      stream.removeListener(\"end\", handler);\n      stream.removeListener(\"error\", handler);\n    };\n\n    stream.on(\"close\", handler);\n    stream.on(\"end\", handler);\n    stream.on(\"error\", handler);\n  });\n}\n\nfunction isArrayBuffer(body: any): body is ArrayBuffer | ArrayBufferView {\n  return body && typeof body.byteLength === \"number\";\n}\n\nclass ReportTransform extends Transform {\n  private loadedBytes = 0;\n  private progressCallback: (progress: TransferProgressEvent) => void;\n\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\n  _transform(chunk: string | Buffer, _encoding: string, callback: Function): void {\n    this.push(chunk);\n    this.loadedBytes += chunk.length;\n    try {\n      this.progressCallback({ loadedBytes: this.loadedBytes });\n      callback();\n    } catch (e: any) {\n      callback(e);\n    }\n  }\n\n  constructor(progressCallback: (progress: TransferProgressEvent) => void) {\n    super();\n    this.progressCallback = progressCallback;\n  }\n}\n\n/**\n * A HttpClient implementation that uses Node's \"https\" module to send HTTPS requests.\n * @internal\n */\nclass NodeHttpClient implements HttpClient {\n  private cachedHttpAgent?: http.Agent;\n  private cachedHttpsAgents: WeakMap<TlsSettings, https.Agent> = new WeakMap();\n\n  /**\n   * Makes a request over an underlying transport layer and returns the response.\n   * @param request - The request to be made.\n   */\n  public async sendRequest(request: PipelineRequest): Promise<PipelineResponse> {\n    const abortController = new AbortController();\n    let abortListener: ((event: any) => void) | undefined;\n    if (request.abortSignal) {\n      if (request.abortSignal.aborted) {\n        throw new AbortError(\"The operation was aborted. Request has already been canceled.\");\n      }\n\n      abortListener = (event: Event) => {\n        if (event.type === \"abort\") {\n          abortController.abort();\n        }\n      };\n      request.abortSignal.addEventListener(\"abort\", abortListener);\n    }\n\n    let timeoutId: ReturnType<typeof setTimeout> | undefined;\n    if (request.timeout > 0) {\n      timeoutId = setTimeout(() => {\n        const sanitizer = new Sanitizer();\n        logger.info(`request to '${sanitizer.sanitizeUrl(request.url)}' timed out. canceling...`);\n        abortController.abort();\n      }, request.timeout);\n    }\n\n    const acceptEncoding = request.headers.get(\"Accept-Encoding\");\n    const shouldDecompress =\n      acceptEncoding?.includes(\"gzip\") || acceptEncoding?.includes(\"deflate\");\n\n    let body = typeof request.body === \"function\" ? request.body() : request.body;\n    if (body && !request.headers.has(\"Content-Length\")) {\n      const bodyLength = getBodyLength(body);\n      if (bodyLength !== null) {\n        request.headers.set(\"Content-Length\", bodyLength);\n      }\n    }\n\n    let responseStream: NodeJS.ReadableStream | undefined;\n    try {\n      if (body && request.onUploadProgress) {\n        const onUploadProgress = request.onUploadProgress;\n        const uploadReportStream = new ReportTransform(onUploadProgress);\n        uploadReportStream.on(\"error\", (e) => {\n          logger.error(\"Error in upload progress\", e);\n        });\n        if (isReadableStream(body)) {\n          body.pipe(uploadReportStream);\n        } else {\n          uploadReportStream.end(body);\n        }\n\n        body = uploadReportStream;\n      }\n\n      const res = await this.makeRequest(request, abortController, body);\n\n      if (timeoutId !== undefined) {\n        clearTimeout(timeoutId);\n      }\n\n      const headers = getResponseHeaders(res);\n\n      const status = res.statusCode ?? 0;\n      const response: PipelineResponse = {\n        status,\n        headers,\n        request,\n      };\n\n      // Responses to HEAD must not have a body.\n      // If they do return a body, that body must be ignored.\n      if (request.method === \"HEAD\") {\n        // call resume() and not destroy() to avoid closing the socket\n        // and losing keep alive\n        res.resume();\n        return response;\n      }\n\n      responseStream = shouldDecompress ? getDecodedResponseStream(res, headers) : res;\n\n      const onDownloadProgress = request.onDownloadProgress;\n      if (onDownloadProgress) {\n        const downloadReportStream = new ReportTransform(onDownloadProgress);\n        downloadReportStream.on(\"error\", (e) => {\n          logger.error(\"Error in download progress\", e);\n        });\n        responseStream.pipe(downloadReportStream);\n        responseStream = downloadReportStream;\n      }\n\n      if (\n        // Value of POSITIVE_INFINITY in streamResponseStatusCodes is considered as any status code\n        request.streamResponseStatusCodes?.has(Number.POSITIVE_INFINITY) ||\n        request.streamResponseStatusCodes?.has(response.status)\n      ) {\n        response.readableStreamBody = responseStream;\n      } else {\n        response.bodyAsText = await streamToText(responseStream);\n      }\n\n      return response;\n    } finally {\n      // clean up event listener\n      if (request.abortSignal && abortListener) {\n        let uploadStreamDone = Promise.resolve();\n        if (isReadableStream(body)) {\n          uploadStreamDone = isStreamComplete(body);\n        }\n        let downloadStreamDone = Promise.resolve();\n        if (isReadableStream(responseStream)) {\n          downloadStreamDone = isStreamComplete(responseStream);\n        }\n        Promise.all([uploadStreamDone, downloadStreamDone])\n          .then(() => {\n            // eslint-disable-next-line promise/always-return\n            if (abortListener) {\n              request.abortSignal?.removeEventListener(\"abort\", abortListener);\n            }\n          })\n          .catch((e) => {\n            logger.warning(\"Error when cleaning up abortListener on httpRequest\", e);\n          });\n      }\n    }\n  }\n\n  private makeRequest(\n    request: PipelineRequest,\n    abortController: AbortController,\n    body?: RequestBodyType,\n  ): Promise<http.IncomingMessage> {\n    const url = new URL(request.url);\n\n    const isInsecure = url.protocol !== \"https:\";\n\n    if (isInsecure && !request.allowInsecureConnection) {\n      throw new Error(`Cannot connect to ${request.url} while allowInsecureConnection is false.`);\n    }\n\n    const agent = (request.agent as http.Agent) ?? this.getOrCreateAgent(request, isInsecure);\n    const options: http.RequestOptions = {\n      agent,\n      hostname: url.hostname,\n      path: `${url.pathname}${url.search}`,\n      port: url.port,\n      method: request.method,\n      headers: request.headers.toJSON({ preserveCase: true }),\n      ...request.requestOverrides,\n    };\n\n    return new Promise<http.IncomingMessage>((resolve, reject) => {\n      const req = isInsecure ? http.request(options, resolve) : https.request(options, resolve);\n\n      req.once(\"error\", (err: Error & { code?: string }) => {\n        reject(\n          new RestError(err.message, { code: err.code ?? RestError.REQUEST_SEND_ERROR, request }),\n        );\n      });\n\n      abortController.signal.addEventListener(\"abort\", () => {\n        const abortError = new AbortError(\n          \"The operation was aborted. Rejecting from abort signal callback while making request.\",\n        );\n        req.destroy(abortError);\n        reject(abortError);\n      });\n      if (body && isReadableStream(body)) {\n        body.pipe(req);\n      } else if (body) {\n        if (typeof body === \"string\" || Buffer.isBuffer(body)) {\n          req.end(body);\n        } else if (isArrayBuffer(body)) {\n          req.end(ArrayBuffer.isView(body) ? Buffer.from(body.buffer) : Buffer.from(body));\n        } else {\n          logger.error(\"Unrecognized body type\", body);\n          reject(new RestError(\"Unrecognized body type\"));\n        }\n      } else {\n        // streams don't like \"undefined\" being passed as data\n        req.end();\n      }\n    });\n  }\n\n  private getOrCreateAgent(request: PipelineRequest, isInsecure: boolean): http.Agent {\n    const disableKeepAlive = request.disableKeepAlive;\n\n    // Handle Insecure requests first\n    if (isInsecure) {\n      if (disableKeepAlive) {\n        // keepAlive:false is the default so we don't need a custom Agent\n        return http.globalAgent;\n      }\n\n      if (!this.cachedHttpAgent) {\n        // If there is no cached agent create a new one and cache it.\n        this.cachedHttpAgent = new http.Agent({ keepAlive: true });\n      }\n      return this.cachedHttpAgent;\n    } else {\n      if (disableKeepAlive && !request.tlsSettings) {\n        // When there are no tlsSettings and keepAlive is false\n        // we don't need a custom agent\n        return https.globalAgent;\n      }\n\n      // We use the tlsSettings to index cached clients\n      const tlsSettings = request.tlsSettings ?? DEFAULT_TLS_SETTINGS;\n\n      // Get the cached agent or create a new one with the\n      // provided values for keepAlive and tlsSettings\n      let agent = this.cachedHttpsAgents.get(tlsSettings);\n\n      if (agent && agent.options.keepAlive === !disableKeepAlive) {\n        return agent;\n      }\n\n      logger.info(\"No cached TLS Agent exist, creating a new Agent\");\n      agent = new https.Agent({\n        // keepAlive is true if disableKeepAlive is false.\n        keepAlive: !disableKeepAlive,\n        // Since we are spreading, if no tslSettings were provided, nothing is added to the agent options.\n        ...tlsSettings,\n      });\n\n      this.cachedHttpsAgents.set(tlsSettings, agent);\n      return agent;\n    }\n  }\n}\n\nfunction getResponseHeaders(res: IncomingMessage): HttpHeaders {\n  const headers = createHttpHeaders();\n  for (const header of Object.keys(res.headers)) {\n    const value = res.headers[header];\n    if (Array.isArray(value)) {\n      if (value.length > 0) {\n        headers.set(header, value[0]);\n      }\n    } else if (value) {\n      headers.set(header, value);\n    }\n  }\n  return headers;\n}\n\nfunction getDecodedResponseStream(\n  stream: IncomingMessage,\n  headers: HttpHeaders,\n): NodeJS.ReadableStream {\n  const contentEncoding = headers.get(\"Content-Encoding\");\n  if (contentEncoding === \"gzip\") {\n    const unzip = zlib.createGunzip();\n    stream.pipe(unzip);\n    return unzip;\n  } else if (contentEncoding === \"deflate\") {\n    const inflate = zlib.createInflate();\n    stream.pipe(inflate);\n    return inflate;\n  }\n\n  return stream;\n}\n\nfunction streamToText(stream: NodeJS.ReadableStream): Promise<string> {\n  return new Promise<string>((resolve, reject) => {\n    const buffer: Buffer[] = [];\n\n    stream.on(\"data\", (chunk) => {\n      if (Buffer.isBuffer(chunk)) {\n        buffer.push(chunk);\n      } else {\n        buffer.push(Buffer.from(chunk));\n      }\n    });\n    stream.on(\"end\", () => {\n      resolve(Buffer.concat(buffer).toString(\"utf8\"));\n    });\n    stream.on(\"error\", (e) => {\n      if (e && e?.name === \"AbortError\") {\n        reject(e);\n      } else {\n        reject(\n          new RestError(`Error reading response as text: ${e.message}`, {\n            code: RestError.PARSE_ERROR,\n          }),\n        );\n      }\n    });\n  });\n}\n\n/** @internal */\nexport function getBodyLength(body: RequestBodyType): number | null {\n  if (!body) {\n    return 0;\n  } else if (Buffer.isBuffer(body)) {\n    return body.length;\n  } else if (isReadableStream(body)) {\n    return null;\n  } else if (isArrayBuffer(body)) {\n    return body.byteLength;\n  } else if (typeof body === \"string\") {\n    return Buffer.from(body).length;\n  } else {\n    return null;\n  }\n}\n\n/**\n * Create a new HttpClient instance for the NodeJS environment.\n * @internal\n */\nexport function createNodeHttpClient(): HttpClient {\n  return new NodeHttpClient();\n}\n"]}