{"version": 3, "file": "defaultRetryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/defaultRetryPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAyBlC,gDAOC;AA5BD,gGAA0F;AAC1F,8FAAwF;AACxF,qDAA+C;AAC/C,kDAA6D;AAE7D;;GAEG;AACU,QAAA,sBAAsB,GAAG,oBAAoB,CAAC;AAO3D;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,UAAqC,EAAE;;IACxE,OAAO;QACL,IAAI,EAAE,8BAAsB;QAC5B,WAAW,EAAE,IAAA,4BAAW,EAAC,CAAC,IAAA,oDAAuB,GAAE,EAAE,IAAA,sDAAwB,EAAC,OAAO,CAAC,CAAC,EAAE;YACvF,UAAU,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,yCAA0B;SAC7D,CAAC,CAAC,WAAW;KACf,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRetryOptions } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { exponentialRetryStrategy } from \"../retryStrategies/exponentialRetryStrategy.js\";\nimport { throttlingRetryStrategy } from \"../retryStrategies/throttlingRetryStrategy.js\";\nimport { retryPolicy } from \"./retryPolicy.js\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants.js\";\n\n/**\n * Name of the {@link defaultRetryPolicy}\n */\nexport const defaultRetryPolicyName = \"defaultRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface DefaultRetryPolicyOptions extends PipelineRetryOptions {}\n\n/**\n * A policy that retries according to three strategies:\n * - When the server sends a 429 response with a Retry-After header.\n * - When there are errors in the underlying transport layer (e.g. DNS lookup failures).\n * - Or otherwise if the outgoing request fails, it will retry with an exponentially increasing delay.\n */\nexport function defaultRetryPolicy(options: DefaultRetryPolicyOptions = {}): PipelinePolicy {\n  return {\n    name: defaultRetryPolicyName,\n    sendRequest: retryPolicy([throttlingRetryStrategy(), exponentialRetryStrategy(options)], {\n      maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n    }).sendRequest,\n  };\n}\n"]}