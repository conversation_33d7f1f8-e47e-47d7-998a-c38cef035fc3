{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/policies/internal.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,mDAAgE;AAAvD,6GAAA,WAAW,OAAA;AAAE,iHAAA,eAAe,OAAA;AACrC,6EAGuC;AAFrC,uIAAA,wBAAwB,OAAA;AACxB,2IAAA,4BAA4B,OAAA;AAE9B,iEAIiC;AAH/B,2HAAA,kBAAkB,OAAA;AAClB,+HAAA,sBAAsB,OAAA;AAGxB,yEAIqC;AAHnC,mIAAA,sBAAsB,OAAA;AACtB,uIAAA,0BAA0B,OAAA;AAG5B,mDAAmE;AAA1D,6GAAA,WAAW,OAAA;AAMpB,yEAAiG;AAAxF,mIAAA,sBAAsB,OAAA;AAAE,uIAAA,0BAA0B,OAAA;AAC3D,uEAA8F;AAArF,iIAAA,qBAAqB,OAAA;AAAE,qIAAA,yBAAyB,OAAA;AACzD,yDAAyE;AAAhE,mHAAA,cAAc,OAAA;AAAE,uHAAA,kBAAkB,OAAA;AAC3C,+CAA4E;AAAnE,yGAAA,SAAS,OAAA;AAAE,6GAAA,aAAa,OAAA;AACjC,2DAA4E;AAAnE,qHAAA,eAAe,OAAA;AAAE,yHAAA,mBAAmB,OAAA;AAC7C,mDAAyF;AAAhF,6GAAA,WAAW,OAAA;AAAE,iHAAA,eAAe,OAAA;AAAE,yHAAA,uBAAuB,OAAA;AAC9D,yDAAgG;AAAvF,mHAAA,cAAc,OAAA;AAAE,uHAAA,kBAAkB,OAAA;AAC3C,+CAA0D;AAAjD,yGAAA,SAAS,OAAA;AAAE,6GAAA,aAAa,OAAA;AACjC,2DAAoG;AAA3F,qHAAA,eAAe,OAAA;AAAE,yHAAA,mBAAmB,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport { agentPolicy, agentPolicyName } from \"./agentPolicy.js\";\nexport {\n  decompressResponsePolicy,\n  decompressResponsePolicyName,\n} from \"./decompressResponsePolicy.js\";\nexport {\n  defaultRetryPolicy,\n  defaultRetryPolicyName,\n  DefaultRetryPolicyOptions,\n} from \"./defaultRetryPolicy.js\";\nexport {\n  exponentialRetryPolicy,\n  exponentialRetryPolicyName,\n  ExponentialRetryPolicyOptions,\n} from \"./exponentialRetryPolicy.js\";\nexport { retryPolicy, RetryPolicyOptions } from \"./retryPolicy.js\";\nexport {\n  RetryInformation,\n  RetryModifiers,\n  RetryStrategy,\n} from \"../retryStrategies/retryStrategy.js\";\nexport { systemErrorRetryPolicy, systemErrorRetryPolicyName } from \"./systemErrorRetryPolicy.js\";\nexport { throttlingRetryPolicy, throttlingRetryPolicyName } from \"./throttlingRetryPolicy.js\";\nexport { formDataPolicy, formDataPolicyName } from \"./formDataPolicy.js\";\nexport { logPolicy, logPolicyName, LogPolicyOptions } from \"./logPolicy.js\";\nexport { multipartPolicy, multipartPolicyName } from \"./multipartPolicy.js\";\nexport { proxyPolicy, proxyPolicyName, getDefaultProxySettings } from \"./proxyPolicy.js\";\nexport { redirectPolicy, redirectPolicyName, RedirectPolicyOptions } from \"./redirectPolicy.js\";\nexport { tlsPolicy, tlsPolicyName } from \"./tlsPolicy.js\";\nexport { userAgentPolicy, userAgentPolicyName, UserAgentPolicyOptions } from \"./userAgentPolicy.js\";\n"]}