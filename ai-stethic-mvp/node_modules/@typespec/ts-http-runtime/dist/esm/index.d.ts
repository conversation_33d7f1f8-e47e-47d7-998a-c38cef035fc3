declare global {
    interface FormData {
    }
    interface Blob {
    }
    interface File {
    }
    interface ReadableStream<R = any> {
    }
    interface TransformStream<I = any, O = any> {
    }
}
export { AbortError } from "./abort-controller/AbortError.js";
export { createClientLogger, getLogLevel, setLogLevel, TypeSpecRuntimeLogger, type Debugger, type TypeSpecRuntimeClientLogger, type TypeSpecRuntimeLogLevel, } from "./logger/logger.js";
export type { BodyPart, FormDataValue, RawHttpHeaders, KeyObject, PxfObject, HttpClient, PipelineRequest, PipelineResponse, SendRequest, TlsSettings, Agent, RequestBodyType, FormDataMap, HttpHeaders, HttpMethods, MultipartRequestBody, TransferProgressEvent, ProxySettings, RawHttpHeadersInput, PipelineRetryOptions, } from "./interfaces.js";
export { createHttpHeaders } from "./httpHeaders.js";
export * from "./auth/schemes.js";
export * from "./auth/oauth2Flows.js";
export { type BasicCredential, type BearerTokenCredential, type OAuth2TokenCredential, type GetOAuth2TokenOptions, type GetBearerTokenOptions, type ApiKeyCredential, type ClientCredential, } from "./auth/credentials.js";
export { createPipelineRequest, type PipelineRequestOptions } from "./pipelineRequest.js";
export { type Pipeline, type PipelinePolicy, type AddPolicyOptions, type PipelinePhase, createEmptyPipeline, } from "./pipeline.js";
export { RestError, isRestError, type RestErrorOptions } from "./restError.js";
export { stringToUint8Array, uint8ArrayToString, type EncodingType } from "./util/bytesEncoding.js";
export { createDefaultHttpClient } from "./defaultHttpClient.js";
export { getClient } from "./client/getClient.js";
export { operationOptionsToRequestParameters } from "./client/operationOptionHelpers.js";
export { createRestError } from "./client/restError.js";
export type { Client, ClientOptions, OperationOptions, AdditionalPolicyConfig, PathUnchecked, PathUncheckedResponse, HttpResponse, RawResponseCallback, OperationRequestOptions, PathParameters, ResourceMethods, PathParameterWithOptions, StreamableMethod, RequestParameters, HttpNodeStreamResponse, HttpBrowserStreamResponse, FullOperationResponse, } from "./client/common.js";
export type { PipelineOptions, TelemetryOptions } from "./createPipelineFromOptions.js";
export type { LogPolicyOptions } from "./policies/logPolicy.js";
export type { RedirectPolicyOptions } from "./policies/redirectPolicy.js";
export type { UserAgentPolicyOptions } from "./policies/userAgentPolicy.js";
//# sourceMappingURL=index.d.ts.map