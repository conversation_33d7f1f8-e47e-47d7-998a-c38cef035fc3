{"version": 3, "file": "interfaces.js", "sourceRoot": "", "sources": ["../../src/interfaces.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthScheme } from \"./auth/schemes.js\";\n\n/**\n * A HttpHeaders collection represented as a simple JSON object.\n */\nexport type RawHttpHeaders = { [headerName: string]: string };\n\n/**\n * A HttpHeaders collection for input, represented as a simple JSON object.\n */\nexport type RawHttpHeadersInput = Record<string, string | number | boolean>;\n\n/**\n * Represents a set of HTTP headers on a request/response.\n * Header names are treated as case insensitive.\n */\nexport interface HttpHeaders extends Iterable<[string, string]> {\n  /**\n   * Returns the value of a specific header or undefined if not set.\n   * @param name - The name of the header to retrieve.\n   */\n  get(name: string): string | undefined;\n  /**\n   * Returns true if the specified header exists.\n   * @param name - The name of the header to check.\n   */\n  has(name: string): boolean;\n  /**\n   * Sets a specific header with a given value.\n   * @param name - The name of the header to set.\n   * @param value - The value to use for the header.\n   */\n  set(name: string, value: string | number | boolean): void;\n  /**\n   * Removes a specific header from the collection.\n   * @param name - The name of the header to delete.\n   */\n  delete(name: string): void;\n  /**\n   * Accesses a raw JS object that acts as a simple map\n   * of header names to values.\n   */\n  toJSON(options?: { preserveCase?: boolean }): RawHttpHeaders;\n}\n\n/**\n * A part of the request body in a multipart request.\n */\nexport interface BodyPart {\n  /**\n   * The headers for this part of the multipart request.\n   */\n  headers: HttpHeaders;\n\n  /**\n   * The body of this part of the multipart request.\n   */\n  body:\n    | ((() => ReadableStream<Uint8Array>) | (() => NodeJS.ReadableStream))\n    | ReadableStream<Uint8Array>\n    | NodeJS.ReadableStream\n    | Uint8Array\n    | Blob;\n}\n\n/**\n * A request body consisting of multiple parts.\n */\nexport interface MultipartRequestBody {\n  /**\n   * The parts of the request body.\n   */\n  parts: BodyPart[];\n\n  /**\n   * The boundary separating each part of the request body.\n   * If not specified, a random boundary will be generated.\n   *\n   * When specified, '--' will be prepended to the boundary in the request to ensure the boundary follows the specification.\n   */\n  boundary?: string;\n}\n\n/**\n * Types of bodies supported on the request.\n * NodeJS.ReadableStream and () =\\> NodeJS.ReadableStream is Node only.\n * Blob, ReadableStream<Uint8Array>, and () =\\> ReadableStream<Uint8Array> are browser only.\n */\nexport type RequestBodyType =\n  | NodeJS.ReadableStream\n  | (() => NodeJS.ReadableStream)\n  | ReadableStream<Uint8Array>\n  | (() => ReadableStream<Uint8Array>)\n  | Blob\n  | ArrayBuffer\n  | ArrayBufferView\n  | FormData\n  | string\n  | null;\n\n/**\n * An interface compatible with NodeJS's `http.Agent`.\n * We want to avoid publicly re-exporting the actual interface,\n * since it might vary across runtime versions.\n */\nexport interface Agent {\n  /**\n   * Destroy any sockets that are currently in use by the agent.\n   */\n  destroy(): void;\n  /**\n   * For agents with keepAlive enabled, this sets the maximum number of sockets that will be left open in the free state.\n   */\n  maxFreeSockets: number;\n  /**\n   * Determines how many concurrent sockets the agent can have open per origin.\n   */\n  maxSockets: number;\n  /**\n   * An object which contains queues of requests that have not yet been assigned to sockets.\n   */\n  requests: unknown;\n  /**\n   * An object which contains arrays of sockets currently in use by the agent.\n   */\n  sockets: unknown;\n}\n\n/**\n * Metadata about a request being made by the pipeline.\n */\nexport interface PipelineRequest {\n  /**\n   * List of authentication schemes used for this specific request.\n   * These schemes define how the request will be authenticated.\n   * If provided, these schemes override the client level authentication schemes.\n   *\n   * If no auth schemes are provided at client or request level, defaults to no auth.\n   */\n  authSchemes?: AuthScheme[];\n\n  /**\n   * The URL to make the request to.\n   */\n  url: string;\n\n  /**\n   * The HTTP method to use when making the request.\n   */\n  method: HttpMethods;\n\n  /**\n   * The HTTP headers to use when making the request.\n   */\n  headers: HttpHeaders;\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   * Defaults to 0, which disables the timeout.\n   */\n  timeout: number;\n\n  /**\n   * Indicates whether the user agent should send cookies from the other domain in the case of cross-origin requests.\n   * Defaults to false.\n   */\n  withCredentials: boolean;\n\n  /**\n   * A unique identifier for the request. Used for logging and tracing.\n   */\n  requestId: string;\n\n  /**\n   * The HTTP body content (if any)\n   */\n  body?: RequestBodyType;\n\n  /**\n   * Body for a multipart request.\n   */\n  multipartBody?: MultipartRequestBody;\n\n  /**\n   * To simulate a browser form post\n   */\n  formData?: FormDataMap;\n\n  /**\n   * A list of response status codes whose corresponding PipelineResponse body should be treated as a stream.\n   * When streamResponseStatusCodes contains the value Number.POSITIVE_INFINITY any status would be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n\n  /**\n   * Proxy configuration.\n   */\n  proxySettings?: ProxySettings;\n\n  /**\n   * If the connection should not be reused.\n   */\n  disableKeepAlive?: boolean;\n\n  /**\n   * Used to abort the request later.\n   */\n  abortSignal?: AbortSignal;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Callback which fires upon download progress. */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Set to true if the request is sent over HTTP instead of HTTPS */\n  allowInsecureConnection?: boolean;\n\n  /**\n   * NODEJS ONLY\n   *\n   * A Node-only option to provide a custom `http.Agent`/`https.Agent`.\n   * Does nothing when running in the browser.\n   */\n  agent?: Agent;\n\n  /**\n   * BROWSER ONLY\n   *\n   * A browser only option to enable browser Streams. If this option is set and a response is a stream\n   * the response will have a property `browserStream` instead of `blobBody` which will be undefined.\n   *\n   * Default value is false\n   */\n  enableBrowserStreams?: boolean;\n\n  /** Settings for configuring TLS authentication */\n  tlsSettings?: TlsSettings;\n\n  /**\n   * Additional options to set on the request. This provides a way to override\n   * existing ones or provide request properties that are not declared.\n   *\n   * For possible valid properties, see\n   *   - NodeJS https.request options:  https://nodejs.org/api/http.html#httprequestoptions-callback\n   *   - Browser RequestInit: https://developer.mozilla.org/en-US/docs/Web/API/RequestInit\n   *\n   * WARNING: Options specified here will override any properties of same names when request is sent by {@link HttpClient}.\n   */\n  requestOverrides?: Record<string, unknown>;\n}\n\n/**\n * Metadata about a response received by the pipeline.\n */\nexport interface PipelineResponse {\n  /**\n   * The request that generated this response.\n   */\n  request: PipelineRequest;\n  /**\n   * The HTTP status code of the response.\n   */\n  status: number;\n  /**\n   * The HTTP response headers.\n   */\n  headers: HttpHeaders;\n\n  /**\n   * The response body as text (string format)\n   */\n  bodyAsText?: string | null;\n\n  /**\n   * BROWSER ONLY\n   *\n   * The response body as a browser Blob.\n   * Always undefined in node.js.\n   */\n  blobBody?: Promise<Blob>;\n\n  /**\n   * BROWSER ONLY\n   *\n   * The response body as a browser ReadableStream.\n   * Always undefined in node.js.\n   */\n  browserStreamBody?: ReadableStream<Uint8Array>;\n\n  /**\n   * NODEJS ONLY\n   *\n   * The response body as a node.js Readable stream.\n   * Always undefined in the browser.\n   */\n  readableStreamBody?: NodeJS.ReadableStream;\n}\n\n/**\n * A simple interface for making a pipeline request and receiving a response.\n */\nexport type SendRequest = (request: PipelineRequest) => Promise<PipelineResponse>;\n\n/**\n * The required interface for a client that makes HTTP requests\n * on behalf of a pipeline.\n */\nexport interface HttpClient {\n  /**\n   * The method that makes the request and returns a response.\n   */\n  sendRequest: SendRequest;\n}\n\n/**\n * Fired in response to upload or download progress.\n */\nexport type TransferProgressEvent = {\n  /**\n   * The number of bytes loaded so far.\n   */\n  loadedBytes: number;\n};\n\n// UNBRANDED DIFFERENCE: HttpMethods are defined at the top level in unbranded instead of core-util since we don't\n//                       need to worry about creating a cyclic dependency\n/**\n * Supported HTTP methods to use when making requests.\n */\nexport type HttpMethods =\n  | \"GET\"\n  | \"PUT\"\n  | \"POST\"\n  | \"DELETE\"\n  | \"PATCH\"\n  | \"HEAD\"\n  | \"OPTIONS\"\n  | \"TRACE\";\n\n/**\n * Options to configure a proxy for outgoing requests (Node.js only).\n */\nexport interface ProxySettings {\n  /**\n   * The proxy's host address.\n   * Must include the protocol (e.g., http:// or https://).\n   */\n  host: string;\n\n  /**\n   * The proxy host's port.\n   */\n  port: number;\n\n  /**\n   * The user name to authenticate with the proxy, if required.\n   */\n  username?: string;\n\n  /**\n   * The password to authenticate with the proxy, if required.\n   */\n  password?: string;\n}\n\n/**\n * Each form data entry can be a string, Blob, or a File. If you wish to pass a file with a name but do not have\n * access to the File class, you can use the createFile helper to create one.\n */\nexport type FormDataValue = string | Blob | File;\n\n/**\n * A simple object that provides form data, as if from a browser form.\n */\nexport type FormDataMap = { [key: string]: FormDataValue | FormDataValue[] };\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface PipelineRetryOptions {\n  /**\n   * The maximum number of retry attempts. Defaults to 3.\n   */\n  maxRetries?: number;\n\n  /**\n   * The amount of delay in milliseconds between retry attempts. Defaults to 1000\n   * (1 second). The delay increases exponentially with each retry up to a maximum\n   * specified by maxRetryDelayInMs.\n   */\n  retryDelayInMs?: number;\n\n  /**\n   * The maximum delay in milliseconds allowed before retrying an operation. Defaults\n   * to 64000 (64 seconds).\n   */\n  maxRetryDelayInMs?: number;\n}\n\n/**\n * Represents a certificate credential for authentication.\n */\nexport interface CertificateCredential {\n  /**\n   * Optionally override the trusted CA certificates. Default is to trust\n   * the well-known CAs curated by Mozilla. Mozilla's CAs are completely\n   * replaced when CAs are explicitly specified using this option.\n   */\n  ca?: string | Buffer | Array<string | Buffer> | undefined;\n  /**\n   *  Cert chains in PEM format. One cert chain should be provided per\n   *  private key. Each cert chain should consist of the PEM formatted\n   *  certificate for a provided private key, followed by the PEM\n   *  formatted intermediate certificates (if any), in order, and not\n   *  including the root CA (the root CA must be pre-known to the peer,\n   *  see ca). When providing multiple cert chains, they do not have to\n   *  be in the same order as their private keys in key. If the\n   *  intermediate certificates are not provided, the peer will not be\n   *  able to validate the certificate, and the handshake will fail.\n   */\n  cert?: string | Buffer | Array<string | Buffer> | undefined;\n  /**\n   * Private keys in PEM format. PEM allows the option of private keys\n   * being encrypted. Encrypted keys will be decrypted with\n   * options.passphrase. Multiple keys using different algorithms can be\n   * provided either as an array of unencrypted key strings or buffers,\n   * or an array of objects in the form `{pem: <string|buffer>[,passphrase: <string>]}`.\n   * The object form can only occur in an array.object.passphrase is optional.\n   * Encrypted keys will be decrypted with object.passphrase if provided, or options.passphrase if it is not.\n   */\n  key?: string | Buffer | Array<Buffer | KeyObject> | undefined;\n  /**\n   * Shared passphrase used for a single private key and/or a PFX.\n   */\n  passphrase?: string | undefined;\n  /**\n   * PFX or PKCS12 encoded private key and certificate chain. pfx is an\n   * alternative to providing key and cert individually. PFX is usually\n   * encrypted, if it is, passphrase will be used to decrypt it. Multiple\n   * PFX can be provided either as an array of unencrypted PFX buffers,\n   * or an array of objects in the form `{buf: <string|buffer>[,passphrase: <string>]}`.\n   * The object form can only occur in an array.object.passphrase is optional.\n   * Encrypted PFX will be decrypted with object.passphrase if provided, or options.passphrase if it is not.\n   */\n  pfx?: string | Buffer | Array<string | Buffer | PxfObject> | undefined;\n}\n\n/**\n * Represents a certificate for TLS authentication.\n */\nexport interface TlsSettings {\n  /**\n   * Optionally override the trusted CA certificates. Default is to trust\n   * the well-known CAs curated by Mozilla. Mozilla's CAs are completely\n   * replaced when CAs are explicitly specified using this option.\n   */\n  ca?: string | Buffer | Array<string | Buffer> | undefined;\n  /**\n   *  Cert chains in PEM format. One cert chain should be provided per\n   *  private key. Each cert chain should consist of the PEM formatted\n   *  certificate for a provided private key, followed by the PEM\n   *  formatted intermediate certificates (if any), in order, and not\n   *  including the root CA (the root CA must be pre-known to the peer,\n   *  see ca). When providing multiple cert chains, they do not have to\n   *  be in the same order as their private keys in key. If the\n   *  intermediate certificates are not provided, the peer will not be\n   *  able to validate the certificate, and the handshake will fail.\n   */\n  cert?: string | Buffer | Array<string | Buffer> | undefined;\n  /**\n   * Private keys in PEM format. PEM allows the option of private keys\n   * being encrypted. Encrypted keys will be decrypted with\n   * options.passphrase. Multiple keys using different algorithms can be\n   * provided either as an array of unencrypted key strings or buffers,\n   * or an array of objects in the form `{pem: <string|buffer>[,passphrase: <string>]}`.\n   * The object form can only occur in an array.object.passphrase is optional.\n   * Encrypted keys will be decrypted with object.passphrase if provided, or options.passphrase if it is not.\n   */\n  key?: string | Buffer | Array<Buffer | KeyObject> | undefined;\n  /**\n   * Shared passphrase used for a single private key and/or a PFX.\n   */\n  passphrase?: string | undefined;\n  /**\n   * PFX or PKCS12 encoded private key and certificate chain. pfx is an\n   * alternative to providing key and cert individually. PFX is usually\n   * encrypted, if it is, passphrase will be used to decrypt it. Multiple\n   * PFX can be provided either as an array of unencrypted PFX buffers,\n   * or an array of objects in the form `{buf: <string|buffer>[,passphrase: <string>]}`.\n   * The object form can only occur in an array.object.passphrase is optional.\n   * Encrypted PFX will be decrypted with object.passphrase if provided, or options.passphrase if it is not.\n   */\n  pfx?: string | Buffer | Array<string | Buffer | PxfObject> | undefined;\n}\n\n/**\n * An interface compatible with NodeJS's `tls.KeyObject`.\n * We want to avoid publicly re-exporting the actual interface,\n * since it might vary across runtime versions.\n */\nexport interface KeyObject {\n  /**\n   * Private keys in PEM format.\n   */\n  pem: string | Buffer;\n  /**\n   * Optional passphrase.\n   */\n  passphrase?: string | undefined;\n}\n\n/**\n * An interface compatible with NodeJS's `tls.PxfObject`.\n * We want to avoid publicly re-exporting the actual interface,\n * since it might vary across runtime versions.\n */\nexport interface PxfObject {\n  /**\n   * PFX or PKCS12 encoded private key and certificate chain.\n   */\n  buf: string | Buffer;\n  /**\n   * Optional passphrase.\n   */\n  passphrase?: string | undefined;\n}\n"]}