{"version": 3, "file": "proxyPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/proxyPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAQlD,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAEnC,MAAM,WAAW,GAAG,aAAa,CAAC;AAClC,MAAM,UAAU,GAAG,YAAY,CAAC;AAChC,MAAM,SAAS,GAAG,WAAW,CAAC;AAC9B,MAAM,QAAQ,GAAG,UAAU,CAAC;AAE5B;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,aAAa,CAAC;AAE7C;;;GAGG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAa,EAAE,CAAC;AAC9C,IAAI,iBAAiB,GAAY,KAAK,CAAC;AAEvC,yDAAyD;AACzD,MAAM,iBAAiB,GAAyB,IAAI,GAAG,EAAE,CAAC;AAE1D,SAAS,mBAAmB,CAAC,IAAY;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACtB,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;QAC3C,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IACzC,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,yBAAyB;IAChC,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,UAAU,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;IACpD,MAAM,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;IAChD,MAAM,SAAS,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAElD,OAAO,UAAU,IAAI,QAAQ,IAAI,SAAS,CAAC;AAC7C,CAAC;AAED;;;;GAIG;AACH,SAAS,UAAU,CACjB,GAAW,EACX,WAAqB,EACrB,WAAkC;IAElC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;IACnC,IAAI,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IACD,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;QAClC,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACvB,mEAAmE;YACnE,mDAAmD;YACnD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC3B,cAAc,GAAG,IAAI,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,IAAI,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBACpE,cAAc,GAAG,IAAI,CAAC;gBACxB,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;gBACrB,cAAc,GAAG,IAAI,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IACD,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACvC,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,MAAM,UAAU,WAAW;IACzB,MAAM,OAAO,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAC9C,iBAAiB,GAAG,IAAI,CAAC;IACzB,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,OAAO;aACX,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;aAC1B,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,uBAAuB,CAAC,QAAiB;IACvD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,QAAQ,GAAG,yBAAyB,EAAE,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IACnE,OAAO;QACL,IAAI,EAAE,MAAM,GAAG,SAAS,CAAC,QAAQ;QACjC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC;QAC7C,QAAQ,EAAE,SAAS,CAAC,QAAQ;QAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;KAC7B,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,+BAA+B;IACtC,MAAM,QAAQ,GAAG,yBAAyB,EAAE,CAAC;IAC7C,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAClD,CAAC;AAED,SAAS,uBAAuB,CAAC,QAAuB;IACtD,IAAI,cAAmB,CAAC;IACxB,IAAI,CAAC;QACH,cAAc,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAAC,WAAM,CAAC;QACP,MAAM,IAAI,KAAK,CACb,+DAA+D,QAAQ,CAAC,IAAI,IAAI,CACjF,CAAC;IACJ,CAAC;IAED,cAAc,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,cAAc,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;IAC9C,CAAC;IACD,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,cAAc,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;IAC9C,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,sBAAsB,CAC7B,OAAwB,EACxB,YAA0B,EAC1B,QAAa;IAEb,2DAA2D;IAC3D,0CAA0C;IAC1C,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,OAAO;IACT,CAAC;IAED,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAEjC,MAAM,UAAU,GAAG,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;IAE7C,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QACxB,MAAM,CAAC,OAAO,CACZ,uHAAuH,CACxH,CAAC;IACJ,CAAC;IAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;IAEzC,IAAI,UAAU,EAAE,CAAC;QACf,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACjC,YAAY,CAAC,cAAc,GAAG,IAAI,cAAc,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1E,CAAC;QACD,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,cAAc,CAAC;IAC9C,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;YAClC,YAAY,CAAC,eAAe,GAAG,IAAI,eAAe,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;QAC5E,CAAC;QACD,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,eAAe,CAAC;IAC/C,CAAC;AACH,CAAC;AAOD;;;;;;GAMG;AACH,MAAM,UAAU,WAAW,CACzB,aAA6B,EAC7B,OAGC;IAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,iBAAiB,CAAC,IAAI,CAAC,GAAG,WAAW,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,YAAY,GAAG,aAAa;QAChC,CAAC,CAAC,uBAAuB,CAAC,aAAa,CAAC;QACxC,CAAC,CAAC,+BAA+B,EAAE,CAAC;IAEtC,MAAM,YAAY,GAAiB,EAAE,CAAC;IAEtC,OAAO;QACL,IAAI,EAAE,eAAe;QACrB,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;;YAC3D,IACE,CAAC,OAAO,CAAC,aAAa;gBACtB,YAAY;gBACZ,CAAC,UAAU,CACT,OAAO,CAAC,GAAG,EACX,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB,mCAAI,iBAAiB,EAC/C,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,iBAAiB,EAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,CAC3D,EACD,CAAC;gBACD,sBAAsB,CAAC,OAAO,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;YAC9D,CAAC;iBAAM,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBACjC,sBAAsB,CACpB,OAAO,EACP,YAAY,EACZ,uBAAuB,CAAC,OAAO,CAAC,aAAa,CAAC,CAC/C,CAAC;YACJ,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type * as http from \"http\";\nimport type * as https from \"https\";\nimport { HttpsProxyAgent } from \"https-proxy-agent\";\nimport { HttpProxyAgent } from \"http-proxy-agent\";\nimport type {\n  PipelineRequest,\n  PipelineResponse,\n  ProxySettings,\n  SendRequest,\n} from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { logger } from \"../log.js\";\n\nconst HTTPS_PROXY = \"HTTPS_PROXY\";\nconst HTTP_PROXY = \"HTTP_PROXY\";\nconst ALL_PROXY = \"ALL_PROXY\";\nconst NO_PROXY = \"NO_PROXY\";\n\n/**\n * The programmatic identifier of the proxyPolicy.\n */\nexport const proxyPolicyName = \"proxyPolicy\";\n\n/**\n * Stores the patterns specified in NO_PROXY environment variable.\n * @internal\n */\nexport const globalNoProxyList: string[] = [];\nlet noProxyListLoaded: boolean = false;\n\n/** A cache of whether a host should bypass the proxy. */\nconst globalBypassedMap: Map<string, boolean> = new Map();\n\nfunction getEnvironmentValue(name: string): string | undefined {\n  if (process.env[name]) {\n    return process.env[name];\n  } else if (process.env[name.toLowerCase()]) {\n    return process.env[name.toLowerCase()];\n  }\n  return undefined;\n}\n\nfunction loadEnvironmentProxyValue(): string | undefined {\n  if (!process) {\n    return undefined;\n  }\n\n  const httpsProxy = getEnvironmentValue(HTTPS_PROXY);\n  const allProxy = getEnvironmentValue(ALL_PROXY);\n  const httpProxy = getEnvironmentValue(HTTP_PROXY);\n\n  return httpsProxy || allProxy || httpProxy;\n}\n\n/**\n * Check whether the host of a given `uri` matches any pattern in the no proxy list.\n * If there's a match, any request sent to the same host shouldn't have the proxy settings set.\n * This implementation is a port of https://github.com/Azure/azure-sdk-for-net/blob/8cca811371159e527159c7eb65602477898683e2/sdk/core/Azure.Core/src/Pipeline/Internal/HttpEnvironmentProxy.cs#L210\n */\nfunction isBypassed(\n  uri: string,\n  noProxyList: string[],\n  bypassedMap?: Map<string, boolean>,\n): boolean | undefined {\n  if (noProxyList.length === 0) {\n    return false;\n  }\n  const host = new URL(uri).hostname;\n  if (bypassedMap?.has(host)) {\n    return bypassedMap.get(host);\n  }\n  let isBypassedFlag = false;\n  for (const pattern of noProxyList) {\n    if (pattern[0] === \".\") {\n      // This should match either domain it self or any subdomain or host\n      // .foo.com will match foo.com it self or *.foo.com\n      if (host.endsWith(pattern)) {\n        isBypassedFlag = true;\n      } else {\n        if (host.length === pattern.length - 1 && host === pattern.slice(1)) {\n          isBypassedFlag = true;\n        }\n      }\n    } else {\n      if (host === pattern) {\n        isBypassedFlag = true;\n      }\n    }\n  }\n  bypassedMap?.set(host, isBypassedFlag);\n  return isBypassedFlag;\n}\n\nexport function loadNoProxy(): string[] {\n  const noProxy = getEnvironmentValue(NO_PROXY);\n  noProxyListLoaded = true;\n  if (noProxy) {\n    return noProxy\n      .split(\",\")\n      .map((item) => item.trim())\n      .filter((item) => item.length);\n  }\n\n  return [];\n}\n\n/**\n * This method converts a proxy url into `ProxySettings` for use with ProxyPolicy.\n * If no argument is given, it attempts to parse a proxy URL from the environment\n * variables `HTTPS_PROXY` or `HTTP_PROXY`.\n * @param proxyUrl - The url of the proxy to use. May contain authentication information.\n * @deprecated - Internally this method is no longer necessary when setting proxy information.\n */\nexport function getDefaultProxySettings(proxyUrl?: string): ProxySettings | undefined {\n  if (!proxyUrl) {\n    proxyUrl = loadEnvironmentProxyValue();\n    if (!proxyUrl) {\n      return undefined;\n    }\n  }\n\n  const parsedUrl = new URL(proxyUrl);\n  const schema = parsedUrl.protocol ? parsedUrl.protocol + \"//\" : \"\";\n  return {\n    host: schema + parsedUrl.hostname,\n    port: Number.parseInt(parsedUrl.port || \"80\"),\n    username: parsedUrl.username,\n    password: parsedUrl.password,\n  };\n}\n\n/**\n * This method attempts to parse a proxy URL from the environment\n * variables `HTTPS_PROXY` or `HTTP_PROXY`.\n */\nfunction getDefaultProxySettingsInternal(): URL | undefined {\n  const envProxy = loadEnvironmentProxyValue();\n  return envProxy ? new URL(envProxy) : undefined;\n}\n\nfunction getUrlFromProxySettings(settings: ProxySettings): URL {\n  let parsedProxyUrl: URL;\n  try {\n    parsedProxyUrl = new URL(settings.host);\n  } catch {\n    throw new Error(\n      `Expecting a valid host string in proxy settings, but found \"${settings.host}\".`,\n    );\n  }\n\n  parsedProxyUrl.port = String(settings.port);\n  if (settings.username) {\n    parsedProxyUrl.username = settings.username;\n  }\n  if (settings.password) {\n    parsedProxyUrl.password = settings.password;\n  }\n\n  return parsedProxyUrl;\n}\n\nfunction setProxyAgentOnRequest(\n  request: PipelineRequest,\n  cachedAgents: CachedAgents,\n  proxyUrl: URL,\n): void {\n  // Custom Agent should take precedence so if one is present\n  // we should skip to avoid overwriting it.\n  if (request.agent) {\n    return;\n  }\n\n  const url = new URL(request.url);\n\n  const isInsecure = url.protocol !== \"https:\";\n\n  if (request.tlsSettings) {\n    logger.warning(\n      \"TLS settings are not supported in combination with custom Proxy, certificates provided to the client will be ignored.\",\n    );\n  }\n\n  const headers = request.headers.toJSON();\n\n  if (isInsecure) {\n    if (!cachedAgents.httpProxyAgent) {\n      cachedAgents.httpProxyAgent = new HttpProxyAgent(proxyUrl, { headers });\n    }\n    request.agent = cachedAgents.httpProxyAgent;\n  } else {\n    if (!cachedAgents.httpsProxyAgent) {\n      cachedAgents.httpsProxyAgent = new HttpsProxyAgent(proxyUrl, { headers });\n    }\n    request.agent = cachedAgents.httpsProxyAgent;\n  }\n}\n\ninterface CachedAgents {\n  httpsProxyAgent?: https.Agent;\n  httpProxyAgent?: http.Agent;\n}\n\n/**\n * A policy that allows one to apply proxy settings to all requests.\n * If not passed static settings, they will be retrieved from the HTTPS_PROXY\n * or HTTP_PROXY environment variables.\n * @param proxySettings - ProxySettings to use on each request.\n * @param options - additional settings, for example, custom NO_PROXY patterns\n */\nexport function proxyPolicy(\n  proxySettings?: ProxySettings,\n  options?: {\n    /** a list of patterns to override those loaded from NO_PROXY environment variable. */\n    customNoProxyList?: string[];\n  },\n): PipelinePolicy {\n  if (!noProxyListLoaded) {\n    globalNoProxyList.push(...loadNoProxy());\n  }\n\n  const defaultProxy = proxySettings\n    ? getUrlFromProxySettings(proxySettings)\n    : getDefaultProxySettingsInternal();\n\n  const cachedAgents: CachedAgents = {};\n\n  return {\n    name: proxyPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (\n        !request.proxySettings &&\n        defaultProxy &&\n        !isBypassed(\n          request.url,\n          options?.customNoProxyList ?? globalNoProxyList,\n          options?.customNoProxyList ? undefined : globalBypassedMap,\n        )\n      ) {\n        setProxyAgentOnRequest(request, cachedAgents, defaultProxy);\n      } else if (request.proxySettings) {\n        setProxyAgentOnRequest(\n          request,\n          cachedAgents,\n          getUrlFromProxySettings(request.proxySettings),\n        );\n      }\n      return next(request);\n    },\n  };\n}\n"]}