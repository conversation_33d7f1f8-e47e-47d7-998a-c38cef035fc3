{"version": 3, "file": "pipelineRequest.js", "sourceRoot": "", "sources": ["../../src/pipelineRequest.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAYlC,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AA8GjD,MAAM,mBAAmB;IAqBvB,YAAY,OAA+B;;QACzC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,OAAO,GAAG,MAAA,OAAO,CAAC,OAAO,mCAAI,iBAAiB,EAAE,CAAC;QACtD,IAAI,CAAC,MAAM,GAAG,MAAA,OAAO,CAAC,MAAM,mCAAI,KAAK,CAAC;QACtC,IAAI,CAAC,OAAO,GAAG,MAAA,OAAO,CAAC,OAAO,mCAAI,CAAC,CAAC;QACpC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,gBAAgB,GAAG,MAAA,OAAO,CAAC,gBAAgB,mCAAI,KAAK,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,CAAC;QACnE,IAAI,CAAC,eAAe,GAAG,MAAA,OAAO,CAAC,eAAe,mCAAI,KAAK,CAAC;QACxD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;QACjD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,UAAU,EAAE,CAAC;QACnD,IAAI,CAAC,uBAAuB,GAAG,MAAA,OAAO,CAAC,uBAAuB,mCAAI,KAAK,CAAC;QACxE,IAAI,CAAC,oBAAoB,GAAG,MAAA,OAAO,CAAC,oBAAoB,mCAAI,KAAK,CAAC;QAClE,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;IACnD,CAAC;CACF;AAED;;;;GAIG;AACH,MAAM,UAAU,qBAAqB,CAAC,OAA+B;IACnE,OAAO,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;AAC1C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  FormDataMap,\n  HttpHeaders,\n  HttpMethods,\n  MultipartRequestBody,\n  PipelineRequest,\n  ProxySettings,\n  RequestBodyType,\n  TransferProgressEvent,\n} from \"./interfaces.js\";\nimport { createHttpHeaders } from \"./httpHeaders.js\";\nimport { randomUUID } from \"./util/uuidUtils.js\";\n\n/**\n * Settings to initialize a request.\n * Almost equivalent to Partial<PipelineRequest>, but url is mandatory.\n */\nexport interface PipelineRequestOptions {\n  /**\n   * The URL to make the request to.\n   */\n  url: string;\n\n  /**\n   * The HTTP method to use when making the request.\n   */\n  method?: HttpMethods;\n\n  /**\n   * The HTTP headers to use when making the request.\n   */\n  headers?: HttpHeaders;\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   * Defaults to 0, which disables the timeout.\n   */\n  timeout?: number;\n\n  /**\n   * If credentials (cookies) should be sent along during an XHR.\n   * Defaults to false.\n   */\n  withCredentials?: boolean;\n\n  /**\n   * A unique identifier for the request. Used for logging and tracing.\n   */\n  requestId?: string;\n\n  /**\n   * The HTTP body content (if any)\n   */\n  body?: RequestBodyType;\n\n  /**\n   * Body for a multipart request.\n   */\n  multipartBody?: MultipartRequestBody;\n\n  /**\n   * To simulate a browser form post\n   */\n  formData?: FormDataMap;\n\n  /**\n   * A list of response status codes whose corresponding PipelineResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n\n  /**\n   * BROWSER ONLY\n   *\n   * A browser only option to enable use of the Streams API. If this option is set and streaming is used\n   * (see `streamResponseStatusCodes`), the response will have a property `browserStream` instead of\n   * `blobBody` which will be undefined.\n   *\n   * Default value is false\n   */\n  enableBrowserStreams?: boolean;\n\n  /**\n   * Proxy configuration.\n   */\n  proxySettings?: ProxySettings;\n\n  /**\n   * If the connection should not be reused.\n   */\n  disableKeepAlive?: boolean;\n\n  /**\n   * Used to abort the request later.\n   */\n  abortSignal?: AbortSignal;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Callback which fires upon download progress. */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Set to true if the request is sent over HTTP instead of HTTPS */\n  allowInsecureConnection?: boolean;\n\n  /**\n   * Additional options to set on the request. This provides a way to override\n   * existing ones or provide request properties that are not declared.\n   *\n   * For possible valid properties, see\n   *   - NodeJS https.request options:  https://nodejs.org/api/http.html#httprequestoptions-callback\n   *   - Browser RequestInit: https://developer.mozilla.org/en-US/docs/Web/API/RequestInit\n   *\n   * WARNING: Options specified here will override any properties of same names when request is sent by {@link HttpClient}.\n   */\n  requestOverrides?: Record<string, unknown>;\n}\n\nclass PipelineRequestImpl implements PipelineRequest {\n  public url: string;\n  public method: HttpMethods;\n  public headers: HttpHeaders;\n  public timeout: number;\n  public withCredentials: boolean;\n  public body?: RequestBodyType;\n  public multipartBody?: MultipartRequestBody;\n  public formData?: FormDataMap;\n  public streamResponseStatusCodes?: Set<number>;\n  public enableBrowserStreams: boolean;\n\n  public proxySettings?: ProxySettings;\n  public disableKeepAlive: boolean;\n  public abortSignal?: AbortSignal;\n  public requestId: string;\n  public allowInsecureConnection?: boolean;\n  public onUploadProgress?: (progress: TransferProgressEvent) => void;\n  public onDownloadProgress?: (progress: TransferProgressEvent) => void;\n  public requestOverrides?: Record<string, unknown>;\n\n  constructor(options: PipelineRequestOptions) {\n    this.url = options.url;\n    this.body = options.body;\n    this.headers = options.headers ?? createHttpHeaders();\n    this.method = options.method ?? \"GET\";\n    this.timeout = options.timeout ?? 0;\n    this.multipartBody = options.multipartBody;\n    this.formData = options.formData;\n    this.disableKeepAlive = options.disableKeepAlive ?? false;\n    this.proxySettings = options.proxySettings;\n    this.streamResponseStatusCodes = options.streamResponseStatusCodes;\n    this.withCredentials = options.withCredentials ?? false;\n    this.abortSignal = options.abortSignal;\n    this.onUploadProgress = options.onUploadProgress;\n    this.onDownloadProgress = options.onDownloadProgress;\n    this.requestId = options.requestId || randomUUID();\n    this.allowInsecureConnection = options.allowInsecureConnection ?? false;\n    this.enableBrowserStreams = options.enableBrowserStreams ?? false;\n    this.requestOverrides = options.requestOverrides;\n  }\n}\n\n/**\n * Creates a new pipeline request with the given options.\n * This method is to allow for the easy setting of default values and not required.\n * @param options - The options to create the request with.\n */\nexport function createPipelineRequest(options: PipelineRequestOptions): PipelineRequest {\n  return new PipelineRequestImpl(options);\n}\n"]}