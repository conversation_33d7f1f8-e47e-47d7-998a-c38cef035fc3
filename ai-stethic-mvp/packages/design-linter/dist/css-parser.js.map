{"version": 3, "file": "css-parser.js", "sourceRoot": "", "sources": ["../src/css-parser.ts"], "names": [], "mappings": ";;;;;;AACA,8EAA6C;AAC7C,4DAAmC;AAGnC,MAAa,SAAS;IAIpB,YAAY,UAAsB,EAAE,UAAuB,EAAE;QAC3D,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG;YACb,cAAc,EAAE,CAAC;YACjB,gBAAgB,EAAE,CAAC;YACnB,GAAG,OAAO;SACX,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,OAAe,EAAE,QAAgB;QACtD,MAAM,UAAU,GAAoB,EAAE,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAA,6BAAU,EAAC,OAAO,EAAE;gBAC/B,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE;gBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBACvC,IAAI,IAAI,EAAE,CAAC;oBACT,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC3D,IAAI,SAAS,EAAE,CAAC;wBACd,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC7B,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,4BAA4B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,IAAyB;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;QACpC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC;QAE3B,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,IAAmB,EAAE,QAAgB;QAC/D,yBAAyB;QACzB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAClD,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACpD,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,QAAQ,KAAK,eAAe,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACzD,CAAC;QAED,8BAA8B;QAC9B,IAAI,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC,wBAAwB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,IAAmB,EAAE,QAAgB;QAC/D,MAAM,KAAK,GAAG,IAAA,oBAAS,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YAAE,OAAO,IAAI,CAAC;QAElC,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAErC,kCAAkC;QAClC,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACxD,IAAA,oBAAS,EAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,KAAK,QAAQ,CAChD,CAAC;QAEF,IAAI,aAAa;YAAE,OAAO,IAAI,CAAC,CAAC,iBAAiB;QAEjD,2BAA2B;QAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAE1D,OAAO;YACL,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,WAAW,IAAI,CAAC,KAAK,kBAAkB,YAAY,CAAC,CAAC,CAAC,iBAAiB,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;YAC1G,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,QAAQ,EAAE,OAAO;YACjB,UAAU,EAAE,YAAY,EAAE,IAAI;YAC9B,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;gBACvB,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,YAAY,CAAC,GAAG;aAC1B,CAAC,CAAC,CAAC,SAAS;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,IAAmB,EAAE,QAAgB;QACjE,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE5D,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;YACpC,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACzD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAChF,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAElE,OAAO;oBACL,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,aAAa,OAAO,CAAC,KAAK,kBAAkB,YAAY,CAAC,CAAC,CAAC,iBAAiB,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;oBAChH,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;oBACzB,QAAQ,EAAE,OAAO;oBACjB,UAAU,EAAE,YAAY,EAAE,IAAI;oBAC9B,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;wBACvB,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,YAAY,CAAC,KAAK;qBAC5B,CAAC,CAAC,CAAC,SAAS;iBACd,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,IAAmB,EAAE,QAAgB;QACtE,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxD,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC;QAE/B,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC9D,KAAK,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,CACrC,CAAC;QAEF,IAAI,aAAa;YAAE,OAAO,IAAI,CAAC;QAE/B,MAAM,YAAY,GAAG,IAAI,CAAC,4BAA4B,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAE5E,OAAO;YACL,IAAI,EAAE,cAAc;YACpB,OAAO,EAAE,mBAAmB,IAAI,CAAC,KAAK,kBAAkB,YAAY,CAAC,CAAC,CAAC,iBAAiB,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;YACnH,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;YACzB,QAAQ,EAAE,OAAO;YACjB,UAAU,EAAE,YAAY,EAAE,IAAI;YAC9B,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;gBACvB,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,YAAY,CAAC,KAAK;aAC5B,CAAC,CAAC,CAAC,SAAS;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,IAAmB,EAAE,QAAgB;QACpE,oDAAoD;QACpD,IAAI,IAAI,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YACpC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACxE,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC5D,KAAK,CAAC,UAAU,KAAK,UAAU,CAChC,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,YAAY;oBAClB,OAAO,EAAE,iBAAiB,IAAI,CAAC,KAAK,iBAAiB;oBACrD,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;oBACzB,QAAQ,EAAE,OAAO;iBAClB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iBAAiB;IACT,eAAe,CAAC,QAAgB;QACtC,OAAO,CAAC,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC5F,CAAC;IAEO,iBAAiB,CAAC,QAAgB;QACxC,OAAO,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACjF,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAC3E,CAAC;IAEO,oBAAoB,CAAC,QAAgB;QAC3C,OAAO,CAAC,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACvF,CAAC;IAEO,oBAAoB,CAAC,KAAa;QACxC,MAAM,MAAM,GAA6C,EAAE,CAAC;QAC5D,MAAM,YAAY,GAAG,+BAA+B,CAAC;QACrD,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtB,4CAA4C;YAC5C,IAAI,MAAM,GAAG,QAAQ,CAAC;YACtB,IAAI,IAAI,KAAK,KAAK;gBAAE,MAAM,IAAI,EAAE,CAAC;YACjC,IAAI,IAAI,KAAK,IAAI;gBAAE,MAAM,IAAI,EAAE,CAAC;YAEhC,MAAM,CAAC,IAAI,CAAC;gBACV,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;gBACf,MAAM;aACP,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACrC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAC1D,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC;QAExB,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEtB,IAAI,MAAM,GAAG,QAAQ,CAAC;QACtB,IAAI,IAAI,KAAK,KAAK;YAAE,MAAM,IAAI,EAAE,CAAC;QACjC,IAAI,IAAI,KAAK,IAAI;YAAE,MAAM,IAAI,EAAE,CAAC;QAEhC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;IAC1B,CAAC;IAEO,qBAAqB,CAAC,SAAiB;QAC7C,MAAM,WAAW,GAAG,IAAA,oBAAS,EAAC,SAAS,CAAC,CAAC;QACzC,IAAI,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,WAAW,GAAG,QAAQ,CAAC;QAE3B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YAC3C,MAAM,UAAU,GAAG,IAAA,oBAAS,EAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAEtE,IAAI,QAAQ,GAAG,WAAW,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,CAAC,EAAE,CAAC;gBAC7E,WAAW,GAAG,QAAQ,CAAC;gBACvB,YAAY,GAAG,KAAK,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,uBAAuB,CAAC,YAAoB;QAClD,IAAI,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,WAAW,GAAG,QAAQ,CAAC;QAE3B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,CAAC;YAEvD,IAAI,QAAQ,GAAG,WAAW,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,CAAC,EAAE,CAAC;gBAC/E,WAAW,GAAG,QAAQ,CAAC;gBACvB,YAAY,GAAG,KAAK,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,4BAA4B,CAAC,YAAoB;QACvD,IAAI,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,WAAW,GAAG,QAAQ,CAAC;QAE3B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;YACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,CAAC;YAEvD,IAAI,QAAQ,GAAG,WAAW,EAAE,CAAC;gBAC3B,WAAW,GAAG,QAAQ,CAAC;gBACvB,YAAY,GAAG,KAAK,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,sBAAsB,CAAC,MAA0B,EAAE,MAA0B;QACnF,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAE5B,yCAAyC;QACzC,OAAO,IAAI,CAAC,IAAI,CACd,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAC7B,CAAC;IACJ,CAAC;CACF;AA7TD,8BA6TC"}