{"version": 3, "file": "jsx-parser.js", "sourceRoot": "", "sources": ["../src/jsx-parser.ts"], "names": [], "mappings": ";;;;;;AAAA,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;AAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;AACpD,MAAM,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAClC,4DAAmC;AAGnC,MAAa,SAAS;IAIpB,YAAY,UAAsB,EAAE,UAAuB,EAAE;QAC3D,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,OAAe,EAAE,QAAgB;QACtD,MAAM,UAAU,GAAoB,EAAE,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,EAAE;gBACzB,UAAU,EAAE,QAAQ;gBACpB,OAAO,EAAE,CAAC,KAAK,EAAE,YAAY,CAAC;aAC/B,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YACjD,MAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,CAAC;YAEnE,8CAA8C;YAC9C,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;YAEnE,iCAAiC;YACjC,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;YAElF,4CAA4C;YAC5C,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC,CAAC;QAEpF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,4BAA4B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,GAAQ;QAC7B,MAAM,OAAO,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE1C,QAAQ,CAAC,GAAG,EAAE;YACZ,iBAAiB,CAAC,IAAI;gBACpB,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;gBAEtC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBAClC,IAAI,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;wBAClE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;wBAClC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;oBACjC,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,GAAQ;QACjC,MAAM,QAAQ,GAAuB,EAAE,CAAC;QAExC,QAAQ,CAAC,GAAG,EAAE;YACZ,UAAU,CAAC,IAAI;gBACb,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAChD,IAAI,OAAO,EAAE,CAAC;oBACZ,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;SACF,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,2BAA2B,CAAC,GAAQ;QAC1C,MAAM,WAAW,GAA0D,EAAE,CAAC;QAE9E,QAAQ,CAAC,GAAG,EAAE;YACZ,mBAAmB,CAAC,IAAI;gBACtB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC;gBAChC,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpC,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;oBAC1B,IAAI,GAAG,EAAE,CAAC;wBACR,WAAW,CAAC,IAAI,CAAC;4BACf,IAAI;4BACJ,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI;4BACpB,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM;yBACzB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YACD,kBAAkB,CAAC,IAAI;gBACrB,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5B,CAAC,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;oBAC5F,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;oBAC/B,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;wBAC1B,IAAI,GAAG,EAAE,CAAC;4BACR,WAAW,CAAC,IAAI,CAAC;gCACf,IAAI;gCACJ,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI;gCACpB,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM;6BACzB,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAAY;QAClC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC;QAEjE,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;QACjD,MAAM,KAAK,GAA2B,EAAE,CAAC;QACzC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;QAExB,IAAI,CAAC,GAAG;YAAE,OAAO,IAAI,CAAC;QAEtB,gBAAgB;QAChB,OAAO,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC/C,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAChC,IAAI,SAAS,GAAG,EAAE,CAAC;gBAEnB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;wBAClC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;oBAC/B,CAAC;yBAAM,IAAI,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;wBAC9F,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;oBAC1C,CAAC;gBACH,CAAC;gBAED,KAAK,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO;YACP,KAAK;YACL,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI;YACpB,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM;SACzB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,0BAA0B,CAAC,GAAQ,EAAE,QAAgB;QAC3D,MAAM,UAAU,GAAoB,EAAE,CAAC;QAEvC,QAAQ,CAAC,GAAG,EAAE;YACZ,sBAAsB,CAAC,IAAI;gBACzB,IAAI,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;oBAEzC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACpC,IAAI,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC;4BACxB,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;4BACzD,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;4BAElC,MAAM,YAAY,GAAG,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;4BAClF,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;4BACvC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;4BAErB,IAAI,GAAG,EAAE,CAAC;gCACR,MAAM,SAAS,GAAG,IAAI,CAAC,2BAA2B,CAChD,YAAY,EACZ,aAAa,EACb,GAAG,CAAC,KAAK,CAAC,IAAI,EACd,GAAG,CAAC,KAAK,CAAC,MAAM,EAChB,QAAQ,CACT,CAAC;gCAEF,IAAI,SAAS,EAAE,CAAC;oCACd,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gCAC7B,CAAC;4BACH,CAAC;wBACH,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,wBAAwB,CAC9B,QAA4B,EAC5B,OAA4B,EAC5B,QAAgB;QAEhB,MAAM,UAAU,GAAyB,EAAE,CAAC;QAE5C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,MAAM,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAExE,IAAI,mBAAmB,EAAE,CAAC;gBACxB,oBAAoB;gBACpB,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAChD,IAAI,CAAC,UAAU,IAAI,UAAU,KAAK,mBAAmB,CAAC,UAAU,EAAE,CAAC;oBACjE,UAAU,CAAC,IAAI,CAAC;wBACd,IAAI,EAAE,WAAW;wBACjB,aAAa,EAAE,OAAO,CAAC,OAAO;wBAC9B,aAAa,EAAE,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,gBAAgB;wBAC7D,OAAO,EAAE,yBAAyB,OAAO,CAAC,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAAC,cAAc,SAAS,mBAAmB,CAAC,UAAU,EAAE;wBACvJ,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,MAAM,EAAE,OAAO,CAAC,MAAM;wBACtB,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;wBAC9B,QAAQ,EAAE,OAAO;wBACjB,QAAQ,EAAE;4BACR,IAAI,EAAE,QAAQ;4BACd,OAAO,EAAE,UAAU,OAAO,CAAC,OAAO,UAAU,mBAAmB,CAAC,UAAU,IAAI;yBAC/E;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,uBAAuB;gBACvB,IAAI,mBAAmB,CAAC,aAAa,EAAE,CAAC;oBACtC,mBAAmB,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;wBACvD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;4BACjC,UAAU,CAAC,IAAI,CAAC;gCACd,IAAI,EAAE,WAAW;gCACjB,aAAa,EAAE,OAAO,CAAC,OAAO;gCAC9B,aAAa,EAAE,cAAc;gCAC7B,OAAO,EAAE,yBAAyB,OAAO,CAAC,OAAO,4BAA4B,YAAY,GAAG;gCAC5F,IAAI,EAAE,OAAO,CAAC,IAAI;gCAClB,MAAM,EAAE,OAAO,CAAC,MAAM;gCACtB,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;gCAC9B,QAAQ,EAAE,OAAO;gCACjB,QAAQ,EAAE;oCACR,IAAI,EAAE,KAAK;oCACX,OAAO,EAAE,IAAI,YAAY,KAAK;iCAC/B;6BACF,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;gBAED,yBAAyB;gBACzB,IAAI,mBAAmB,CAAC,eAAe,EAAE,CAAC;oBACxC,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,IAAI,EAAE,CAAC;oBAChD,mBAAmB,CAAC,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;wBAC1D,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;4BACvC,UAAU,CAAC,IAAI,CAAC;gCACd,IAAI,EAAE,WAAW;gCACjB,aAAa,EAAE,OAAO,CAAC,OAAO;gCAC9B,aAAa,EAAE,eAAe;gCAC9B,OAAO,EAAE,yBAAyB,OAAO,CAAC,OAAO,6BAA6B,aAAa,GAAG;gCAC9F,IAAI,EAAE,OAAO,CAAC,IAAI;gCAClB,MAAM,EAAE,OAAO,CAAC,MAAM;gCACtB,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM;gCAC9B,QAAQ,EAAE,OAAO;gCACjB,QAAQ,EAAE;oCACR,IAAI,EAAE,KAAK;oCACX,OAAO,EAAE,IAAI,aAAa,EAAE;iCAC7B;6BACF,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,wBAAwB,CAC9B,WAAkE,EAClE,QAAgB;QAEhB,MAAM,UAAU,GAAyB,EAAE,CAAC;QAE5C,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACxB,MAAM,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEjE,IAAI,mBAAmB,EAAE,CAAC;gBACxB,UAAU,CAAC,IAAI,CAAC;oBACd,IAAI,EAAE,WAAW;oBACjB,aAAa,EAAE,GAAG,CAAC,IAAI;oBACvB,aAAa,EAAE,sBAAsB;oBACrC,OAAO,EAAE,4CAA4C,GAAG,CAAC,IAAI,2EAA2E,mBAAmB,CAAC,UAAU,EAAE;oBACxK,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM;oBACvB,QAAQ,EAAE,OAAO;oBACjB,QAAQ,EAAE;wBACR,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,UAAU,GAAG,CAAC,IAAI,UAAU,mBAAmB,CAAC,UAAU,IAAI;qBACxE;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,2BAA2B,CACjC,QAAgB,EAChB,KAAa,EACb,IAAY,EACZ,MAAc,EACd,QAAgB;QAEhB,yBAAyB;QACzB,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,IAAA,oBAAS,EAAC,KAAK,CAAC,CAAC;YAC/B,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpB,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;gBACrC,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACxD,IAAA,oBAAS,EAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,KAAK,QAAQ,CAChD,CAAC;gBAEF,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,MAAM,YAAY,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;oBAC1D,OAAO;wBACL,IAAI,EAAE,OAAO;wBACb,OAAO,EAAE,WAAW,KAAK,kBAAkB,YAAY,CAAC,CAAC,CAAC,iBAAiB,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;wBACrG,IAAI;wBACJ,MAAM;wBACN,MAAM,EAAE,KAAK,CAAC,MAAM;wBACpB,QAAQ,EAAE,OAAO;wBACjB,UAAU,EAAE,YAAY,EAAE,IAAI;wBAC9B,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;4BACvB,IAAI,EAAE,SAAS;4BACf,OAAO,EAAE,YAAY,CAAC,GAAG;yBAC1B,CAAC,CAAC,CAAC,SAAS;qBACd,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iBAAiB;IACT,YAAY,CAAC,IAAY;QAC/B,OAAO,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAEO,eAAe,CAAC,QAAgB;QACtC,OAAO,CAAC,OAAO,EAAE,iBAAiB,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACxE,CAAC;IAEO,qBAAqB,CAAC,SAAiB;QAC7C,MAAM,WAAW,GAAG,IAAA,oBAAS,EAAC,SAAS,CAAC,CAAC;QACzC,IAAI,YAAY,GAAG,IAAI,CAAC;QACxB,IAAI,WAAW,GAAG,QAAQ,CAAC;QAE3B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YAC3C,MAAM,UAAU,GAAG,IAAA,oBAAS,EAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAEtE,IAAI,QAAQ,GAAG,WAAW,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,CAAC,EAAE,CAAC;gBAC7E,WAAW,GAAG,QAAQ,CAAC;gBACvB,YAAY,GAAG,KAAK,CAAC;YACvB,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAEO,sBAAsB,CAAC,MAA0B,EAAE,MAA0B;QACnF,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAC5B,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;QAE5B,OAAO,IAAI,CAAC,IAAI,CACd,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAC7B,CAAC;IACJ,CAAC;CACF;AA5YD,8BA4YC"}