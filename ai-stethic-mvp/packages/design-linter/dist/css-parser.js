"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CSSParser = void 0;
const postcss_safe_parser_1 = __importDefault(require("postcss-safe-parser"));
const tinycolor2_1 = __importDefault(require("tinycolor2"));
class CSSParser {
    constructor(tokensFile, options = {}) {
        this.tokensFile = tokensFile;
        this.options = {
            colorThreshold: 5,
            spacingThreshold: 2,
            ...options,
        };
    }
    /**
     * Parse CSS/SCSS content and extract design token violations
     */
    parseCSSContent(content, filePath) {
        const violations = [];
        try {
            const root = (0, postcss_safe_parser_1.default)(content, {
                from: filePath
            });
            root.walkDecls((decl) => {
                const rule = this.extractCSSRule(decl);
                if (rule) {
                    const violation = this.checkTokenViolation(rule, filePath);
                    if (violation) {
                        violations.push(violation);
                    }
                }
            });
        }
        catch (error) {
            console.warn(`Failed to parse CSS file ${filePath}:`, error);
        }
        return violations;
    }
    /**
     * Extract CSS rule information from PostCSS declaration
     */
    extractCSSRule(decl) {
        const position = decl.source?.start;
        if (!position)
            return null;
        return {
            property: decl.prop,
            value: decl.value,
            line: position.line,
            column: position.column,
        };
    }
    /**
     * Check if a CSS rule violates design tokens
     */
    checkTokenViolation(rule, filePath) {
        // Check color violations
        if (this.isColorProperty(rule.property)) {
            return this.checkColorViolation(rule, filePath);
        }
        // Check spacing violations
        if (this.isSpacingProperty(rule.property)) {
            return this.checkSpacingViolation(rule, filePath);
        }
        // Check border-radius violations
        if (rule.property === 'border-radius') {
            return this.checkBorderRadiusViolation(rule, filePath);
        }
        // Check typography violations
        if (this.isTypographyProperty(rule.property)) {
            return this.checkTypographyViolation(rule, filePath);
        }
        return null;
    }
    /**
     * Check for color token violations
     */
    checkColorViolation(rule, filePath) {
        const color = (0, tinycolor2_1.default)(rule.value);
        if (!color.isValid())
            return null;
        const colorHex = color.toHexString();
        // Check if color exists in tokens
        const matchingToken = this.tokensFile.colors.find(token => (0, tinycolor2_1.default)(token.hex).toHexString() === colorHex);
        if (matchingToken)
            return null; // Color is valid
        // Find closest color token
        const closestToken = this.findClosestColorToken(colorHex);
        return {
            type: 'color',
            message: `COLOR: "${rule.value}" not in tokens${closestToken ? `. Suggestion: ${closestToken.name}` : ''}`,
            line: rule.line,
            column: rule.column,
            length: rule.value.length,
            severity: 'error',
            suggestion: closestToken?.name,
            quickFix: closestToken ? {
                type: 'replace',
                newText: closestToken.hex,
            } : undefined,
        };
    }
    /**
     * Check for spacing token violations
     */
    checkSpacingViolation(rule, filePath) {
        const spacingValues = this.extractSpacingValues(rule.value);
        for (const spacing of spacingValues) {
            const matchingToken = this.tokensFile.spacing.find(token => Math.abs(token.pixels - spacing.pixels) <= (this.options.spacingThreshold || 2));
            if (!matchingToken) {
                const closestToken = this.findClosestSpacingToken(spacing.pixels);
                return {
                    type: 'spacing',
                    message: `SPACING: "${spacing.value}" not in tokens${closestToken ? `. Suggestion: ${closestToken.value}` : ''}`,
                    line: rule.line,
                    column: rule.column,
                    length: rule.value.length,
                    severity: 'error',
                    suggestion: closestToken?.name,
                    quickFix: closestToken ? {
                        type: 'replace',
                        newText: closestToken.value,
                    } : undefined,
                };
            }
        }
        return null;
    }
    /**
     * Check for border-radius token violations
     */
    checkBorderRadiusViolation(rule, filePath) {
        const borderRadius = this.parseNumericValue(rule.value);
        if (!borderRadius)
            return null;
        const matchingToken = this.tokensFile.borderRadius.find(token => token.pixels === borderRadius.pixels);
        if (matchingToken)
            return null;
        const closestToken = this.findClosestBorderRadiusToken(borderRadius.pixels);
        return {
            type: 'borderRadius',
            message: `BORDER-RADIUS: "${rule.value}" not in tokens${closestToken ? `. Suggestion: ${closestToken.value}` : ''}`,
            line: rule.line,
            column: rule.column,
            length: rule.value.length,
            severity: 'error',
            suggestion: closestToken?.name,
            quickFix: closestToken ? {
                type: 'replace',
                newText: closestToken.value,
            } : undefined,
        };
    }
    /**
     * Check for typography token violations
     */
    checkTypographyViolation(rule, filePath) {
        // For MVP, we'll focus on font-family and font-size
        if (rule.property === 'font-family') {
            const fontFamily = rule.value.replace(/['"]/g, '').split(',')[0].trim();
            const matchingToken = this.tokensFile.typography.find(token => token.fontFamily === fontFamily);
            if (!matchingToken) {
                return {
                    type: 'typography',
                    message: `FONT-FAMILY: "${rule.value}" not in tokens`,
                    line: rule.line,
                    column: rule.column,
                    length: rule.value.length,
                    severity: 'error',
                };
            }
        }
        return null;
    }
    // Helper methods
    isColorProperty(property) {
        return ['color', 'background-color', 'border-color', 'fill', 'stroke'].includes(property);
    }
    isSpacingProperty(property) {
        return ['margin', 'padding', 'gap', 'top', 'right', 'bottom', 'left'].includes(property) ||
            property.startsWith('margin-') || property.startsWith('padding-');
    }
    isTypographyProperty(property) {
        return ['font-family', 'font-size', 'font-weight', 'line-height'].includes(property);
    }
    extractSpacingValues(value) {
        const values = [];
        const spacingRegex = /(\d+(?:\.\d+)?)(px|rem|em|%)/g;
        let match;
        while ((match = spacingRegex.exec(value)) !== null) {
            const numValue = parseFloat(match[1]);
            const unit = match[2];
            // Convert to pixels (simplified conversion)
            let pixels = numValue;
            if (unit === 'rem')
                pixels *= 16;
            if (unit === 'em')
                pixels *= 16;
            values.push({
                value: match[0],
                pixels,
            });
        }
        return values;
    }
    parseNumericValue(value) {
        const match = value.match(/^(\d+(?:\.\d+)?)(px|rem|em)$/);
        if (!match)
            return null;
        const numValue = parseFloat(match[1]);
        const unit = match[2];
        let pixels = numValue;
        if (unit === 'rem')
            pixels *= 16;
        if (unit === 'em')
            pixels *= 16;
        return { pixels, unit };
    }
    findClosestColorToken(targetHex) {
        const targetColor = (0, tinycolor2_1.default)(targetHex);
        let closestToken = null;
        let minDistance = Infinity;
        for (const token of this.tokensFile.colors) {
            const tokenColor = (0, tinycolor2_1.default)(token.hex);
            const distance = this.calculateColorDistance(targetColor, tokenColor);
            if (distance < minDistance && distance <= (this.options.colorThreshold || 5)) {
                minDistance = distance;
                closestToken = token;
            }
        }
        return closestToken;
    }
    findClosestSpacingToken(targetPixels) {
        let closestToken = null;
        let minDistance = Infinity;
        for (const token of this.tokensFile.spacing) {
            const distance = Math.abs(token.pixels - targetPixels);
            if (distance < minDistance && distance <= (this.options.spacingThreshold || 2)) {
                minDistance = distance;
                closestToken = token;
            }
        }
        return closestToken;
    }
    findClosestBorderRadiusToken(targetPixels) {
        let closestToken = null;
        let minDistance = Infinity;
        for (const token of this.tokensFile.borderRadius) {
            const distance = Math.abs(token.pixels - targetPixels);
            if (distance < minDistance) {
                minDistance = distance;
                closestToken = token;
            }
        }
        return closestToken;
    }
    calculateColorDistance(color1, color2) {
        const rgb1 = color1.toRgb();
        const rgb2 = color2.toRgb();
        // Simple Euclidean distance in RGB space
        return Math.sqrt(Math.pow(rgb1.r - rgb2.r, 2) +
            Math.pow(rgb1.g - rgb2.g, 2) +
            Math.pow(rgb1.b - rgb2.b, 2));
    }
}
exports.CSSParser = CSSParser;
//# sourceMappingURL=css-parser.js.map