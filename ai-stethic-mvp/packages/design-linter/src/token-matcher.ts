import tinycolor from 'tinycolor2';
import { TokenCandidate, ColorToken, SpacingToken, TypographyToken, BorderRadiusToken } from './types';

export class TokenMatcher {
  /**
   * Cluster similar color values
   */
  public static clusterColors(candidates: TokenCandidate[], threshold: number = 10): TokenCandidate[] {
    const clusters: TokenCandidate[] = [];
    const processed = new Set<string>();

    candidates.forEach(candidate => {
      if (processed.has(candidate.value)) return;

      const color = tinycolor(candidate.value);
      if (!color.isValid()) return;

      const cluster: TokenCandidate = {
        ...candidate,
        occurrences: [...candidate.occurrences],
      };

      // Find similar colors
      candidates.forEach(other => {
        if (other.value === candidate.value || processed.has(other.value)) return;

        const otherColor = tinycolor(other.value);
        if (!otherColor.isValid()) return;

        const distance = this.calculateColorDistance(color, otherColor);
        if (distance <= threshold) {
          cluster.occurrences.push(...other.occurrences);
          processed.add(other.value);
        }
      });

      clusters.push(cluster);
      processed.add(candidate.value);
    });

    return clusters;
  }

  /**
   * Cluster similar spacing values
   */
  public static clusterSpacing(candidates: TokenCandidate[], threshold: number = 2): TokenCandidate[] {
    const clusters: TokenCandidate[] = [];
    const processed = new Set<string>();

    candidates.forEach(candidate => {
      if (processed.has(candidate.value)) return;

      const pixels = this.parsePixelValue(candidate.value);
      if (pixels === null) return;

      const cluster: TokenCandidate = {
        ...candidate,
        occurrences: [...candidate.occurrences],
      };

      // Find similar spacing values
      candidates.forEach(other => {
        if (other.value === candidate.value || processed.has(other.value)) return;

        const otherPixels = this.parsePixelValue(other.value);
        if (otherPixels === null) return;

        if (Math.abs(pixels - otherPixels) <= threshold) {
          cluster.occurrences.push(...other.occurrences);
          processed.add(other.value);
        }
      });

      clusters.push(cluster);
      processed.add(candidate.value);
    });

    return clusters;
  }

  /**
   * Cluster similar border-radius values
   */
  public static clusterBorderRadius(candidates: TokenCandidate[]): TokenCandidate[] {
    const clusters: TokenCandidate[] = [];
    const processed = new Set<string>();

    candidates.forEach(candidate => {
      if (processed.has(candidate.value)) return;

      const pixels = this.parsePixelValue(candidate.value);
      if (pixels === null) return;

      const cluster: TokenCandidate = {
        ...candidate,
        occurrences: [...candidate.occurrences],
      };

      // Find exact matches for border-radius
      candidates.forEach(other => {
        if (other.value === candidate.value || processed.has(other.value)) return;

        const otherPixels = this.parsePixelValue(other.value);
        if (otherPixels === pixels) {
          cluster.occurrences.push(...other.occurrences);
          processed.add(other.value);
        }
      });

      clusters.push(cluster);
      processed.add(candidate.value);
    });

    return clusters;
  }

  /**
   * Cluster typography values
   */
  public static clusterTypography(candidates: TokenCandidate[]): TokenCandidate[] {
    const fontFamilies = new Map<string, TokenCandidate>();
    const fontSizes = new Map<string, TokenCandidate>();

    candidates.forEach(candidate => {
      if (this.isFontFamily(candidate.value)) {
        const family = this.normalizeFontFamily(candidate.value);
        if (fontFamilies.has(family)) {
          fontFamilies.get(family)!.occurrences.push(...candidate.occurrences);
        } else {
          fontFamilies.set(family, { ...candidate, value: family });
        }
      } else if (this.isFontSize(candidate.value)) {
        if (fontSizes.has(candidate.value)) {
          fontSizes.get(candidate.value)!.occurrences.push(...candidate.occurrences);
        } else {
          fontSizes.set(candidate.value, candidate);
        }
      }
    });

    return [...fontFamilies.values(), ...fontSizes.values()];
  }

  /**
   * Generate suggested token names
   */
  public static generateTokenNames(candidates: TokenCandidate[]): TokenCandidate[] {
    return candidates.map(candidate => {
      let suggestedName = '';

      switch (candidate.type) {
        case 'color':
          suggestedName = this.generateColorName(candidate.value);
          break;
        case 'spacing':
          suggestedName = this.generateSpacingName(candidate.value);
          break;
        case 'typography':
          suggestedName = this.generateTypographyName(candidate.value);
          break;
        case 'borderRadius':
          suggestedName = this.generateBorderRadiusName(candidate.value);
          break;
      }

      return {
        ...candidate,
        suggestedName,
      };
    });
  }

  /**
   * Convert candidates to token objects
   */
  public static candidatesToTokens(candidates: TokenCandidate[]): {
    colors: ColorToken[];
    spacing: SpacingToken[];
    typography: TypographyToken[];
    borderRadius: BorderRadiusToken[];
  } {
    const colors: ColorToken[] = [];
    const spacing: SpacingToken[] = [];
    const typography: TypographyToken[] = [];
    const borderRadius: BorderRadiusToken[] = [];

    candidates.forEach(candidate => {
      const name = candidate.suggestedName || candidate.value;

      switch (candidate.type) {
        case 'color':
          const color = tinycolor(candidate.value);
          if (color.isValid()) {
            colors.push({
              name,
              value: candidate.value,
              type: 'color',
              hex: color.toHexString(),
              rgb: color.toRgb(),
              hsl: color.toHsl(),
            });
          }
          break;

        case 'spacing':
          const spacingPixels = this.parsePixelValue(candidate.value);
          if (spacingPixels !== null) {
            const unit = this.extractUnit(candidate.value) || 'px';
            spacing.push({
              name,
              value: candidate.value,
              type: 'spacing',
              pixels: spacingPixels,
              unit: unit as 'px' | 'rem' | 'em' | '%',
            });
          }
          break;

        case 'typography':
          if (this.isFontFamily(candidate.value)) {
            typography.push({
              name,
              value: candidate.value,
              type: 'typography',
              fontFamily: candidate.value,
            });
          } else if (this.isFontSize(candidate.value)) {
            typography.push({
              name,
              value: candidate.value,
              type: 'typography',
              fontSize: candidate.value,
            });
          }
          break;

        case 'borderRadius':
          const borderPixels = this.parsePixelValue(candidate.value);
          if (borderPixels !== null) {
            const unit = this.extractUnit(candidate.value) || 'px';
            borderRadius.push({
              name,
              value: candidate.value,
              type: 'borderRadius',
              pixels: borderPixels,
              unit: unit as 'px' | 'rem' | 'em',
            });
          }
          break;
      }
    });

    return { colors, spacing, typography, borderRadius };
  }

  // Helper methods
  private static calculateColorDistance(color1: tinycolor.Instance, color2: tinycolor.Instance): number {
    const rgb1 = color1.toRgb();
    const rgb2 = color2.toRgb();
    
    return Math.sqrt(
      Math.pow(rgb1.r - rgb2.r, 2) +
      Math.pow(rgb1.g - rgb2.g, 2) +
      Math.pow(rgb1.b - rgb2.b, 2)
    );
  }

  private static parsePixelValue(value: string): number | null {
    const match = value.match(/^(\d+(?:\.\d+)?)(px|rem|em)$/);
    if (!match) return null;

    const numValue = parseFloat(match[1]);
    const unit = match[2];
    
    // Convert to pixels
    let pixels = numValue;
    if (unit === 'rem') pixels *= 16;
    if (unit === 'em') pixels *= 16;

    return pixels;
  }

  private static extractUnit(value: string): string | null {
    const match = value.match(/\d+(?:\.\d+)?(px|rem|em|%)$/);
    return match ? match[1] : null;
  }

  private static isFontFamily(value: string): boolean {
    return /^[a-zA-Z\s,"'-]+$/.test(value) && !value.match(/^\d/);
  }

  private static isFontSize(value: string): boolean {
    return /^\d+(?:\.\d+)?(px|rem|em|%)$/.test(value);
  }

  private static normalizeFontFamily(value: string): string {
    return value.replace(/['"]/g, '').split(',')[0].trim();
  }

  private static generateColorName(value: string): string {
    const color = tinycolor(value);
    if (!color.isValid()) return 'unknown';

    const hsl = color.toHsl();
    const lightness = Math.round(hsl.l * 100);

    // Basic color name mapping
    const hue = Math.round(hsl.h);
    let baseName = 'gray';

    if (hsl.s > 0.1) {
      if (hue >= 0 && hue < 30) baseName = 'red';
      else if (hue >= 30 && hue < 60) baseName = 'orange';
      else if (hue >= 60 && hue < 120) baseName = 'yellow';
      else if (hue >= 120 && hue < 180) baseName = 'green';
      else if (hue >= 180 && hue < 240) baseName = 'cyan';
      else if (hue >= 240 && hue < 300) baseName = 'blue';
      else if (hue >= 300 && hue < 360) baseName = 'purple';
    }

    if (lightness > 80) return `${baseName}-light`;
    if (lightness < 20) return `${baseName}-dark`;
    return baseName;
  }

  private static generateSpacingName(value: string): string {
    const pixels = this.parsePixelValue(value);
    if (pixels === null) return 'spacing';

    if (pixels <= 4) return 'xs';
    if (pixels <= 8) return 'sm';
    if (pixels <= 16) return 'md';
    if (pixels <= 24) return 'lg';
    if (pixels <= 32) return 'xl';
    return 'xxl';
  }

  private static generateTypographyName(value: string): string {
    if (this.isFontFamily(value)) {
      const family = this.normalizeFontFamily(value).toLowerCase();
      if (family.includes('serif')) return 'serif';
      if (family.includes('mono')) return 'mono';
      return 'sans';
    }

    if (this.isFontSize(value)) {
      const pixels = this.parsePixelValue(value);
      if (pixels === null) return 'text';

      if (pixels <= 12) return 'text-xs';
      if (pixels <= 14) return 'text-sm';
      if (pixels <= 16) return 'text-base';
      if (pixels <= 18) return 'text-lg';
      if (pixels <= 24) return 'text-xl';
      return 'text-2xl';
    }

    return 'text';
  }

  private static generateBorderRadiusName(value: string): string {
    const pixels = this.parsePixelValue(value);
    if (pixels === null) return 'rounded';

    if (pixels <= 2) return 'rounded-sm';
    if (pixels <= 4) return 'rounded';
    if (pixels <= 8) return 'rounded-md';
    if (pixels <= 12) return 'rounded-lg';
    return 'rounded-xl';
  }
}
