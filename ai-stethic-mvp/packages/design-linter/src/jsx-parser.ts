const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const t = require('@babel/types');
import tinycolor from 'tinycolor2';
import { ParsedJSXElement, LintViolation, TokensFile, LintOptions, ComponentViolation } from './types';

export class JSXParser {
  private tokensFile: TokensFile;
  private options: LintOptions;

  constructor(tokensFile: TokensFile, options: LintOptions = {}) {
    this.tokensFile = tokensFile;
    this.options = options;
  }

  /**
   * Parse JSX/TSX content and extract violations
   */
  public parseJSXContent(content: string, filePath: string): LintViolation[] {
    const violations: LintViolation[] = [];
    
    try {
      const ast = parse(content, {
        sourceType: 'module',
        plugins: ['jsx', 'typescript'],
      });

      const imports = this.extractImports(ast);
      const jsxElements = this.extractJSXElements(ast);
      const componentDefinitions = this.extractComponentDefinitions(ast);

      // Check for token violations in inline styles
      violations.push(...this.checkInlineStyleViolations(ast, filePath));

      // Check for component violations
      violations.push(...this.checkComponentViolations(jsxElements, imports, filePath));

      // Check for duplicate component definitions
      violations.push(...this.checkDuplicateComponents(componentDefinitions, filePath));

    } catch (error) {
      console.warn(`Failed to parse JSX file ${filePath}:`, error);
    }

    return violations;
  }

  /**
   * Extract import statements from AST
   */
  private extractImports(ast: any): Map<string, string> {
    const imports = new Map<string, string>();

    traverse(ast, {
      ImportDeclaration(path) {
        const source = path.node.source.value;
        
        path.node.specifiers.forEach(spec => {
          if (t.isImportDefaultSpecifier(spec) || t.isImportSpecifier(spec)) {
            const localName = spec.local.name;
            imports.set(localName, source);
          }
        });
      },
    });

    return imports;
  }

  /**
   * Extract JSX elements from AST
   */
  private extractJSXElements(ast: any): ParsedJSXElement[] {
    const elements: ParsedJSXElement[] = [];

    traverse(ast, {
      JSXElement(path) {
        const element = this.parseJSXElement(path.node);
        if (element) {
          elements.push(element);
        }
      },
    });

    return elements;
  }

  /**
   * Extract component definitions from AST
   */
  private extractComponentDefinitions(ast: any): Array<{ name: string; line: number; column: number }> {
    const definitions: Array<{ name: string; line: number; column: number }> = [];

    traverse(ast, {
      FunctionDeclaration(path) {
        const name = path.node.id?.name;
        if (name && this.isPascalCase(name)) {
          const loc = path.node.loc;
          if (loc) {
            definitions.push({
              name,
              line: loc.start.line,
              column: loc.start.column,
            });
          }
        }
      },
      VariableDeclarator(path) {
        if (t.isIdentifier(path.node.id) && 
            (t.isArrowFunctionExpression(path.node.init) || t.isFunctionExpression(path.node.init))) {
          const name = path.node.id.name;
          if (this.isPascalCase(name)) {
            const loc = path.node.loc;
            if (loc) {
              definitions.push({
                name,
                line: loc.start.line,
                column: loc.start.column,
              });
            }
          }
        }
      },
    });

    return definitions;
  }

  /**
   * Parse individual JSX element
   */
  private parseJSXElement(element: any): ParsedJSXElement | null {
    if (!t.isJSXIdentifier(element.openingElement.name)) return null;

    const tagName = element.openingElement.name.name;
    const props: Record<string, string> = {};
    const loc = element.loc;

    if (!loc) return null;

    // Extract props
    element.openingElement.attributes.forEach(attr => {
      if (t.isJSXAttribute(attr) && t.isJSXIdentifier(attr.name)) {
        const propName = attr.name.name;
        let propValue = '';

        if (attr.value) {
          if (t.isStringLiteral(attr.value)) {
            propValue = attr.value.value;
          } else if (t.isJSXExpressionContainer(attr.value) && t.isStringLiteral(attr.value.expression)) {
            propValue = attr.value.expression.value;
          }
        }

        props[propName] = propValue;
      }
    });

    return {
      tagName,
      props,
      line: loc.start.line,
      column: loc.start.column,
    };
  }

  /**
   * Check for inline style violations
   */
  private checkInlineStyleViolations(ast: any, filePath: string): LintViolation[] {
    const violations: LintViolation[] = [];

    traverse(ast, {
      JSXExpressionContainer(path) {
        if (t.isObjectExpression(path.node.expression)) {
          const styleObject = path.node.expression;
          
          styleObject.properties.forEach(prop => {
            if (t.isObjectProperty(prop) && 
                (t.isStringLiteral(prop.key) || t.isIdentifier(prop.key)) &&
                t.isStringLiteral(prop.value)) {
              
              const propertyName = t.isStringLiteral(prop.key) ? prop.key.value : prop.key.name;
              const propertyValue = prop.value.value;
              const loc = prop.loc;

              if (loc) {
                const violation = this.checkStylePropertyViolation(
                  propertyName,
                  propertyValue,
                  loc.start.line,
                  loc.start.column,
                  filePath
                );
                
                if (violation) {
                  violations.push(violation);
                }
              }
            }
          });
        }
      },
    });

    return violations;
  }

  /**
   * Check for component violations
   */
  private checkComponentViolations(
    elements: ParsedJSXElement[],
    imports: Map<string, string>,
    filePath: string
  ): ComponentViolation[] {
    const violations: ComponentViolation[] = [];

    elements.forEach(element => {
      const registeredComponent = this.tokensFile.components[element.tagName];
      
      if (registeredComponent) {
        // Check import path
        const importPath = imports.get(element.tagName);
        if (!importPath || importPath !== registeredComponent.importPath) {
          violations.push({
            type: 'component',
            componentName: element.tagName,
            violationType: importPath ? 'wrong-import' : 'missing-import',
            message: `Component Violation: <${element.tagName}> ${importPath ? 'imported from wrong path' : 'not imported'} from ${registeredComponent.importPath}`,
            line: element.line,
            column: element.column,
            length: element.tagName.length,
            severity: 'error',
            quickFix: {
              type: 'import',
              newText: `import ${element.tagName} from '${registeredComponent.importPath}';`,
            },
          });
        }

        // Check required props
        if (registeredComponent.requiredProps) {
          registeredComponent.requiredProps.forEach(requiredProp => {
            if (!element.props[requiredProp]) {
              violations.push({
                type: 'component',
                componentName: element.tagName,
                violationType: 'missing-prop',
                message: `Component Violation: <${element.tagName}> missing required prop '${requiredProp}'`,
                line: element.line,
                column: element.column,
                length: element.tagName.length,
                severity: 'error',
                quickFix: {
                  type: 'add',
                  newText: ` ${requiredProp}=""`,
                },
              });
            }
          });
        }

        // Check required classes
        if (registeredComponent.requiredClasses) {
          const className = element.props.className || '';
          registeredComponent.requiredClasses.forEach(requiredClass => {
            if (!className.includes(requiredClass)) {
              violations.push({
                type: 'component',
                componentName: element.tagName,
                violationType: 'missing-class',
                message: `Component Violation: <${element.tagName}> missing required class '${requiredClass}'`,
                line: element.line,
                column: element.column,
                length: element.tagName.length,
                severity: 'error',
                quickFix: {
                  type: 'add',
                  newText: ` ${requiredClass}`,
                },
              });
            }
          });
        }
      }
    });

    return violations;
  }

  /**
   * Check for duplicate component definitions
   */
  private checkDuplicateComponents(
    definitions: Array<{ name: string; line: number; column: number }>,
    filePath: string
  ): ComponentViolation[] {
    const violations: ComponentViolation[] = [];

    definitions.forEach(def => {
      const registeredComponent = this.tokensFile.components[def.name];
      
      if (registeredComponent) {
        violations.push({
          type: 'component',
          componentName: def.name,
          violationType: 'duplicate-definition',
          message: `Component Violation: local definition of ${def.name} conflicts with registered component. Delete definition and import from ${registeredComponent.importPath}`,
          line: def.line,
          column: def.column,
          length: def.name.length,
          severity: 'error',
          quickFix: {
            type: 'replace',
            newText: `import ${def.name} from '${registeredComponent.importPath}';`,
          },
        });
      }
    });

    return violations;
  }

  /**
   * Check style property for token violations
   */
  private checkStylePropertyViolation(
    property: string,
    value: string,
    line: number,
    column: number,
    filePath: string
  ): LintViolation | null {
    // Check color properties
    if (this.isColorProperty(property)) {
      const color = tinycolor(value);
      if (color.isValid()) {
        const colorHex = color.toHexString();
        const matchingToken = this.tokensFile.colors.find(token => 
          tinycolor(token.hex).toHexString() === colorHex
        );

        if (!matchingToken) {
          const closestToken = this.findClosestColorToken(colorHex);
          return {
            type: 'color',
            message: `COLOR: "${value}" not in tokens${closestToken ? `. Suggestion: ${closestToken.name}` : ''}`,
            line,
            column,
            length: value.length,
            severity: 'error',
            suggestion: closestToken?.name,
            quickFix: closestToken ? {
              type: 'replace',
              newText: closestToken.hex,
            } : undefined,
          };
        }
      }
    }

    return null;
  }

  // Helper methods
  private isPascalCase(name: string): boolean {
    return /^[A-Z][a-zA-Z0-9]*$/.test(name);
  }

  private isColorProperty(property: string): boolean {
    return ['color', 'backgroundColor', 'borderColor'].includes(property);
  }

  private findClosestColorToken(targetHex: string) {
    const targetColor = tinycolor(targetHex);
    let closestToken = null;
    let minDistance = Infinity;

    for (const token of this.tokensFile.colors) {
      const tokenColor = tinycolor(token.hex);
      const distance = this.calculateColorDistance(targetColor, tokenColor);
      
      if (distance < minDistance && distance <= (this.options.colorThreshold || 5)) {
        minDistance = distance;
        closestToken = token;
      }
    }

    return closestToken;
  }

  private calculateColorDistance(color1: tinycolor.Instance, color2: tinycolor.Instance): number {
    const rgb1 = color1.toRgb();
    const rgb2 = color2.toRgb();
    
    return Math.sqrt(
      Math.pow(rgb1.r - rgb2.r, 2) +
      Math.pow(rgb1.g - rgb2.g, 2) +
      Math.pow(rgb1.b - rgb2.b, 2)
    );
  }
}
