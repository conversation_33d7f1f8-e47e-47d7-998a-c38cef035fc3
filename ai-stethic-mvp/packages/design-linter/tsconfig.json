{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "noImplicitAny": false, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}