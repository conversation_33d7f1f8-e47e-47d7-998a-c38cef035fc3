"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenWizard = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
class TokenWizard {
    constructor(linter, workspaceRoot) {
        this.linter = linter;
        this.workspaceRoot = workspaceRoot;
    }
    async run() {
        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "AI-Stethic: Initializing Design Tokens",
                cancellable: false
            }, async (progress) => {
                progress.report({ increment: 0, message: "Starting token wizard..." });
                // Step 1: Choose initialization method
                const initMethod = await this.chooseInitializationMethod();
                if (!initMethod)
                    return;
                progress.report({ increment: 20, message: "Scanning workspace..." });
                let tokensFile;
                if (initMethod === 'import') {
                    tokensFile = await this.importExistingTokens();
                }
                else if (initMethod === 'scan') {
                    tokensFile = await this.scanAndCreateTokens(progress);
                }
                else {
                    tokensFile = this.createEmptyTokensFile();
                }
                if (!tokensFile)
                    return;
                progress.report({ increment: 80, message: "Saving tokens file..." });
                // Save tokens file
                const config = vscode.workspace.getConfiguration('aiStethic');
                const tokensFilePath = path.join(this.workspaceRoot, config.get('tokensFile', '.ai-stethic/tokens.json'));
                this.linter.saveTokensFile(tokensFile, tokensFilePath);
                progress.report({ increment: 100, message: "Complete!" });
                vscode.window.showInformationMessage(`AI-Stethic: Design tokens initialized successfully! Found ${tokensFile.colors.length} colors, ${tokensFile.spacing.length} spacing values, ${tokensFile.typography.length} typography tokens, and ${tokensFile.borderRadius.length} border-radius values.`);
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to initialize tokens: ${error}`);
        }
    }
    async chooseInitializationMethod() {
        const options = [
            {
                label: '🔍 Scan Codebase',
                description: 'Automatically detect tokens from existing CSS/JSX files',
                value: 'scan'
            },
            {
                label: '📁 Import JSON',
                description: 'Import from an existing tokens.json file',
                value: 'import'
            },
            {
                label: '📝 Start Blank',
                description: 'Create an empty tokens file',
                value: 'blank'
            }
        ];
        const selection = await vscode.window.showQuickPick(options, {
            placeHolder: 'How would you like to initialize your design tokens?'
        });
        return selection?.value;
    }
    async importExistingTokens() {
        const fileUri = await vscode.window.showOpenDialog({
            canSelectFiles: true,
            canSelectFolders: false,
            canSelectMany: false,
            filters: {
                'JSON files': ['json']
            },
            openLabel: 'Import Tokens'
        });
        if (!fileUri || fileUri.length === 0)
            return undefined;
        try {
            const fs = require('fs');
            const content = fs.readFileSync(fileUri[0].fsPath, 'utf-8');
            const imported = JSON.parse(content);
            // Validate structure
            if (!this.validateTokensFileStructure(imported)) {
                vscode.window.showErrorMessage('Invalid tokens file structure. Please ensure it contains colors, spacing, typography, borderRadius, and components sections.');
                return undefined;
            }
            return imported;
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to import tokens file: ${error}`);
            return undefined;
        }
    }
    async scanAndCreateTokens(progress) {
        try {
            // Scan workspace for token candidates
            const candidates = await this.linter.scanWorkspaceForTokens(this.workspaceRoot);
            progress.report({ increment: 40, message: "Processing color tokens..." });
            // Process colors
            const selectedColors = await this.selectTokenCandidates(candidates.colors, 'color', 'Select colors to include in your design system:');
            progress.report({ increment: 50, message: "Processing spacing tokens..." });
            // Process spacing
            const selectedSpacing = await this.selectTokenCandidates(candidates.spacing, 'spacing', 'Select spacing values to include:');
            progress.report({ increment: 60, message: "Processing typography tokens..." });
            // Process typography
            const selectedTypography = await this.selectTokenCandidates(candidates.typography, 'typography', 'Select typography tokens to include:');
            progress.report({ increment: 70, message: "Processing border-radius tokens..." });
            // Process border-radius
            const selectedBorderRadius = await this.selectTokenCandidates(candidates.borderRadius, 'borderRadius', 'Select border-radius values to include:');
            // Create tokens file
            const tokensFile = this.linter.createTokensFile({
                colors: selectedColors,
                spacing: selectedSpacing,
                typography: selectedTypography,
                borderRadius: selectedBorderRadius
            });
            return tokensFile;
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to scan workspace: ${error}`);
            return undefined;
        }
    }
    async selectTokenCandidates(candidates, type, prompt) {
        if (candidates.length === 0) {
            return [];
        }
        const items = candidates.map(candidate => ({
            label: candidate.suggestedName || candidate.value,
            description: candidate.value,
            detail: `Found in ${candidate.occurrences.length} location(s)`,
            candidate
        }));
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: prompt,
            canPickMany: true,
            ignoreFocusOut: true
        });
        if (!selected)
            return [];
        // Allow users to rename tokens
        const finalCandidates = [];
        for (const item of selected) {
            const name = await vscode.window.showInputBox({
                prompt: `Name for ${type} token`,
                value: item.candidate.suggestedName || item.candidate.value,
                placeHolder: 'Enter token name'
            });
            if (name) {
                finalCandidates.push({
                    ...item.candidate,
                    suggestedName: name
                });
            }
        }
        return finalCandidates;
    }
    createEmptyTokensFile() {
        return {
            colors: [],
            spacing: [],
            typography: [],
            borderRadius: [],
            components: {}
        };
    }
    validateTokensFileStructure(obj) {
        return obj &&
            Array.isArray(obj.colors) &&
            Array.isArray(obj.spacing) &&
            Array.isArray(obj.typography) &&
            Array.isArray(obj.borderRadius) &&
            typeof obj.components === 'object';
    }
}
exports.TokenWizard = TokenWizard;
//# sourceMappingURL=token-wizard.js.map