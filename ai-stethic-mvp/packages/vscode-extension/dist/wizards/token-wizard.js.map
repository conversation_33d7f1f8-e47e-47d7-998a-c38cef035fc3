{"version": 3, "file": "token-wizard.js", "sourceRoot": "", "sources": ["../../src/wizards/token-wizard.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAA6B;AAG7B,MAAa,WAAW;IAIpB,YAAY,MAAoB,EAAE,aAAqB;QACnD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACvC,CAAC;IAEM,KAAK,CAAC,GAAG;QACZ,IAAI,CAAC;YACD,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,wCAAwC;gBAC/C,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;gBAClB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;gBAEvE,uCAAuC;gBACvC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAC3D,IAAI,CAAC,UAAU;oBAAE,OAAO;gBAExB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;gBAErE,IAAI,UAAkC,CAAC;gBAEvC,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;oBAC1B,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBACnD,CAAC;qBAAM,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;oBAC/B,UAAU,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBAC1D,CAAC;qBAAM,CAAC;oBACJ,UAAU,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC9C,CAAC;gBAED,IAAI,CAAC,UAAU;oBAAE,OAAO;gBAExB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;gBAErE,mBAAmB;gBACnB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;gBAC9D,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAC5B,IAAI,CAAC,aAAa,EAClB,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,yBAAyB,CAAC,CACtD,CAAC;gBAEF,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;gBAEvD,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;gBAE1D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,6DAA6D,UAAU,CAAC,MAAM,CAAC,MAAM,YAAY,UAAU,CAAC,OAAO,CAAC,MAAM,oBAAoB,UAAU,CAAC,UAAU,CAAC,MAAM,2BAA2B,UAAU,CAAC,YAAY,CAAC,MAAM,wBAAwB,CAC9P,CAAC;YACN,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,KAAK,EAAE,CAAC,CAAC;QAC5E,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACpC,MAAM,OAAO,GAAG;YACZ;gBACI,KAAK,EAAE,kBAAkB;gBACzB,WAAW,EAAE,yDAAyD;gBACtE,KAAK,EAAE,MAAM;aAChB;YACD;gBACI,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,0CAA0C;gBACvD,KAAK,EAAE,QAAQ;aAClB;YACD;gBACI,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,6BAA6B;gBAC1C,KAAK,EAAE,OAAO;aACjB;SACJ,CAAC;QAEF,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAE;YACzD,WAAW,EAAE,sDAAsD;SACtE,CAAC,CAAC;QAEH,OAAO,SAAS,EAAE,KAAK,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAC9B,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;YAC/C,cAAc,EAAE,IAAI;YACpB,gBAAgB,EAAE,KAAK;YACvB,aAAa,EAAE,KAAK;YACpB,OAAO,EAAE;gBACL,YAAY,EAAE,CAAC,MAAM,CAAC;aACzB;YACD,SAAS,EAAE,eAAe;SAC7B,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,SAAS,CAAC;QAEvD,IAAI,CAAC;YACD,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;YACzB,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAErC,qBAAqB;YACrB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8HAA8H,CAAC,CAAC;gBAC/J,OAAO,SAAS,CAAC;YACrB,CAAC;YAED,OAAO,QAAsB,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,KAAK,EAAE,CAAC,CAAC;YACzE,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,QAAmE;QACjG,IAAI,CAAC;YACD,sCAAsC;YACtC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEhF,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE,CAAC,CAAC;YAE1E,iBAAiB;YACjB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACnD,UAAU,CAAC,MAAM,EACjB,OAAO,EACP,iDAAiD,CACpD,CAAC;YAEF,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,8BAA8B,EAAE,CAAC,CAAC;YAE5E,kBAAkB;YAClB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACpD,UAAU,CAAC,OAAO,EAClB,SAAS,EACT,mCAAmC,CACtC,CAAC;YAEF,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC,CAAC;YAE/E,qBAAqB;YACrB,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACvD,UAAU,CAAC,UAAU,EACrB,YAAY,EACZ,sCAAsC,CACzC,CAAC;YAEF,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC,CAAC;YAElF,wBAAwB;YACxB,MAAM,oBAAoB,GAAG,MAAM,IAAI,CAAC,qBAAqB,CACzD,UAAU,CAAC,YAAY,EACvB,cAAc,EACd,yCAAyC,CAC5C,CAAC;YAEF,qBAAqB;YACrB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBAC5C,MAAM,EAAE,cAAc;gBACtB,OAAO,EAAE,eAAe;gBACxB,UAAU,EAAE,kBAAkB;gBAC9B,YAAY,EAAE,oBAAoB;aACrC,CAAC,CAAC;YAEH,OAAO,UAAU,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;YACrE,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAC/B,UAA4B,EAC5B,IAAY,EACZ,MAAc;QAEd,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,EAAE,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;YACvC,KAAK,EAAE,SAAS,CAAC,aAAa,IAAI,SAAS,CAAC,KAAK;YACjD,WAAW,EAAE,SAAS,CAAC,KAAK;YAC5B,MAAM,EAAE,YAAY,SAAS,CAAC,WAAW,CAAC,MAAM,cAAc;YAC9D,SAAS;SACZ,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE;YACtD,WAAW,EAAE,MAAM;YACnB,WAAW,EAAE,IAAI;YACjB,cAAc,EAAE,IAAI;SACvB,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ;YAAE,OAAO,EAAE,CAAC;QAEzB,+BAA+B;QAC/B,MAAM,eAAe,GAAqB,EAAE,CAAC;QAC7C,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC1B,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC1C,MAAM,EAAE,YAAY,IAAI,QAAQ;gBAChC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK;gBAC3D,WAAW,EAAE,kBAAkB;aAClC,CAAC,CAAC;YAEH,IAAI,IAAI,EAAE,CAAC;gBACP,eAAe,CAAC,IAAI,CAAC;oBACjB,GAAG,IAAI,CAAC,SAAS;oBACjB,aAAa,EAAE,IAAI;iBACtB,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAED,OAAO,eAAe,CAAC;IAC3B,CAAC;IAEO,qBAAqB;QACzB,OAAO;YACH,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,UAAU,EAAE,EAAE;YACd,YAAY,EAAE,EAAE;YAChB,UAAU,EAAE,EAAE;SACjB,CAAC;IACN,CAAC;IAEO,2BAA2B,CAAC,GAAQ;QACxC,OAAO,GAAG;YACH,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;YACzB,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;YAC1B,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;YAC7B,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;YAC/B,OAAO,GAAG,CAAC,UAAU,KAAK,QAAQ,CAAC;IAC9C,CAAC;CACJ;AAzOD,kCAyOC"}