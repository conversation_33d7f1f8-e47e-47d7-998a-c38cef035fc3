"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComponentWizard = void 0;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
class ComponentWizard {
    constructor(linter, workspaceRoot) {
        this.linter = linter;
        this.workspaceRoot = workspaceRoot;
    }
    async run() {
        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "AI-Stethic: Registering Components",
                cancellable: false
            }, async (progress) => {
                progress.report({ increment: 0, message: "Scanning for components..." });
                // Get component directory from configuration
                const config = vscode.workspace.getConfiguration('aiStethic');
                const componentDir = config.get('componentDirectory', 'src/components/ui');
                // Scan for components
                const components = await this.linter.scanWorkspaceForComponents(this.workspaceRoot, componentDir);
                if (components.length === 0) {
                    vscode.window.showInformationMessage(`No React components found in ${componentDir}. Make sure your components are in the correct directory.`);
                    return;
                }
                progress.report({ increment: 30, message: "Selecting components..." });
                // Let user select which components to register
                const selectedComponents = await this.selectComponents(components);
                if (!selectedComponents || selectedComponents.length === 0) {
                    return;
                }
                progress.report({ increment: 60, message: "Configuring components..." });
                // Configure each selected component
                const configuredComponents = [];
                for (const component of selectedComponents) {
                    const configured = await this.configureComponent(component);
                    if (configured) {
                        configuredComponents.push(configured);
                    }
                }
                progress.report({ increment: 80, message: "Updating tokens file..." });
                // Update tokens file
                await this.updateTokensFile(configuredComponents);
                progress.report({ increment: 100, message: "Complete!" });
                vscode.window.showInformationMessage(`AI-Stethic: Successfully registered ${configuredComponents.length} component(s)!`);
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to register components: ${error}`);
        }
    }
    async selectComponents(components) {
        const items = components.map(component => ({
            label: component.name,
            description: component.importPath,
            detail: 'React component',
            component
        }));
        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select components to register in your design system:',
            canPickMany: true,
            ignoreFocusOut: true
        });
        return selected?.map(item => item.component);
    }
    async configureComponent(component) {
        vscode.window.showInformationMessage(`Configuring component: ${component.name}`);
        // Ask for required props
        const requiredPropsInput = await vscode.window.showInputBox({
            prompt: `Enter required props for ${component.name} (comma-separated)`,
            placeHolder: 'e.g., variant, size, disabled',
            value: component.requiredProps?.join(', ') || ''
        });
        const requiredProps = requiredPropsInput
            ? requiredPropsInput.split(',').map(prop => prop.trim()).filter(prop => prop.length > 0)
            : [];
        // Ask for required classes
        const requiredClassesInput = await vscode.window.showInputBox({
            prompt: `Enter required CSS classes for ${component.name} (comma-separated)`,
            placeHolder: 'e.g., btn, card, modal',
            value: component.requiredClasses?.join(', ') || ''
        });
        const requiredClasses = requiredClassesInput
            ? requiredClassesInput.split(',').map(cls => cls.trim()).filter(cls => cls.length > 0)
            : [];
        // Confirm import path
        const importPath = await vscode.window.showInputBox({
            prompt: `Confirm import path for ${component.name}`,
            value: component.importPath,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'Import path is required';
                }
                return null;
            }
        });
        if (!importPath)
            return undefined;
        return {
            name: component.name,
            importPath: importPath.trim(),
            requiredProps,
            requiredClasses
        };
    }
    async updateTokensFile(components) {
        const config = vscode.workspace.getConfiguration('aiStethic');
        const tokensFilePath = path.join(this.workspaceRoot, config.get('tokensFile', '.ai-stethic/tokens.json'));
        try {
            // Load existing tokens file
            const fs = require('fs');
            let tokensFile;
            if (fs.existsSync(tokensFilePath)) {
                const content = fs.readFileSync(tokensFilePath, 'utf-8');
                tokensFile = JSON.parse(content);
            }
            else {
                // Create new tokens file if it doesn't exist
                tokensFile = {
                    colors: [],
                    spacing: [],
                    typography: [],
                    borderRadius: [],
                    components: {}
                };
            }
            // Add components to tokens file
            components.forEach(component => {
                tokensFile.components[component.name] = component;
            });
            // Save updated tokens file
            this.linter.saveTokensFile(tokensFile, tokensFilePath);
        }
        catch (error) {
            throw new Error(`Failed to update tokens file: ${error}`);
        }
    }
    async registerSingleComponent(componentName, importPath) {
        try {
            const component = {
                name: componentName,
                importPath,
                requiredProps: [],
                requiredClasses: []
            };
            const configured = await this.configureComponent(component);
            if (configured) {
                await this.updateTokensFile([configured]);
                vscode.window.showInformationMessage(`AI-Stethic: Successfully registered component "${componentName}"!`);
            }
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to register component: ${error}`);
        }
    }
}
exports.ComponentWizard = ComponentWizard;
//# sourceMappingURL=component-wizard.js.map