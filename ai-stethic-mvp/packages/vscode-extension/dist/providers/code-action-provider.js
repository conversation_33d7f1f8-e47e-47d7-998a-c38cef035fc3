"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeActionProvider = void 0;
const vscode = __importStar(require("vscode"));
class CodeActionProvider {
    constructor(linter, tokensFilePath) {
        this.linter = linter;
        this.tokensFilePath = tokensFilePath;
    }
    provideCodeActions(document, range, context, token) {
        const actions = [];
        // Filter diagnostics to only AI-Stethic ones
        const aiStethicDiagnostics = context.diagnostics.filter(diagnostic => diagnostic.source === 'AI-Stethic');
        for (const diagnostic of aiStethicDiagnostics) {
            const violation = this.extractViolationFromDiagnostic(document, diagnostic);
            if (violation) {
                actions.push(...this.createCodeActionsForViolation(document, diagnostic, violation));
            }
        }
        return actions;
    }
    extractViolationFromDiagnostic(document, diagnostic) {
        // Re-lint the document to get the violation details
        const content = document.getText();
        const filePath = document.uri.fsPath;
        let violations = [];
        try {
            if (document.languageId === 'css' || document.languageId === 'scss') {
                violations = this.linter.lintCss(content, filePath);
            }
            else if (document.languageId === 'javascriptreact' || document.languageId === 'typescriptreact') {
                violations.push(...this.linter.lintJsx(content, filePath));
                violations.push(...this.linter.lintComponents(content, filePath));
            }
        }
        catch (error) {
            console.error('Error re-linting document for code actions:', error);
            return null;
        }
        // Find the violation that matches this diagnostic
        const line = diagnostic.range.start.line + 1; // Convert to 1-based
        const column = diagnostic.range.start.character;
        return violations.find(v => v.line === line &&
            Math.abs(v.column - column) <= 2 && // Allow small column differences
            v.message === diagnostic.message) || null;
    }
    createCodeActionsForViolation(document, diagnostic, violation) {
        const actions = [];
        // Create quick fix action if available
        if (violation.quickFix) {
            const quickFixAction = this.createQuickFixAction(document, diagnostic, violation.quickFix);
            if (quickFixAction) {
                actions.push(quickFixAction);
            }
        }
        // Create "Add as new token" action for token violations
        if (violation.type !== 'component' && !violation.suggestion) {
            const addTokenAction = this.createAddTokenAction(document, diagnostic, violation);
            if (addTokenAction) {
                actions.push(addTokenAction);
            }
        }
        // Create "Register component" action for unregistered components
        if (violation.type === 'component' && violation.message.includes('not imported')) {
            const registerComponentAction = this.createRegisterComponentAction(document, diagnostic);
            if (registerComponentAction) {
                actions.push(registerComponentAction);
            }
        }
        return actions;
    }
    createQuickFixAction(document, diagnostic, quickFix) {
        const action = new vscode.CodeAction(`Replace with ${quickFix.newText}`, vscode.CodeActionKind.QuickFix);
        action.diagnostics = [diagnostic];
        action.isPreferred = true;
        const edit = new vscode.WorkspaceEdit();
        if (quickFix.type === 'replace') {
            edit.replace(document.uri, diagnostic.range, quickFix.newText);
        }
        else if (quickFix.type === 'add') {
            // For adding props or classes, insert at the end of the opening tag
            const line = diagnostic.range.start.line;
            const lineText = document.lineAt(line).text;
            const tagEnd = lineText.indexOf('>', diagnostic.range.start.character);
            if (tagEnd !== -1) {
                const insertPosition = new vscode.Position(line, tagEnd);
                edit.insert(document.uri, insertPosition, quickFix.newText);
            }
        }
        else if (quickFix.type === 'import') {
            // Add import at the top of the file
            const firstLine = new vscode.Position(0, 0);
            edit.insert(document.uri, firstLine, quickFix.newText + '\n');
        }
        action.edit = edit;
        return action;
    }
    createAddTokenAction(document, diagnostic, violation) {
        const value = document.getText(diagnostic.range);
        const action = new vscode.CodeAction(`Add "${value}" as new ${violation.type} token`, vscode.CodeActionKind.QuickFix);
        action.diagnostics = [diagnostic];
        // Create command to add token
        action.command = {
            command: 'aiStethic.addToken',
            title: 'Add Token',
            arguments: [value, violation.type]
        };
        return action;
    }
    createRegisterComponentAction(document, diagnostic) {
        const action = new vscode.CodeAction('Register component in design system', vscode.CodeActionKind.QuickFix);
        action.diagnostics = [diagnostic];
        // Create command to register component
        action.command = {
            command: 'aiStethic.registerComponents',
            title: 'Register Components'
        };
        return action;
    }
    updateLinter(linter) {
        this.linter = linter;
    }
    updateTokensFilePath(tokensFilePath) {
        this.tokensFilePath = tokensFilePath;
    }
}
exports.CodeActionProvider = CodeActionProvider;
//# sourceMappingURL=code-action-provider.js.map