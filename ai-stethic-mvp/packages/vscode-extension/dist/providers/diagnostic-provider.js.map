{"version": 3, "file": "diagnostic-provider.js", "sourceRoot": "", "sources": ["../../src/providers/diagnostic-provider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,MAAa,kBAAkB;IAI3B,YAAY,MAAoB,EAAE,MAAqC;QACnE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAEM,iBAAiB,CACpB,QAA6B,EAC7B,UAAuC;QAEvC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAChC,OAAO;QACX,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;QAElF,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IAC9C,CAAC;IAEO,kBAAkB,CAAC,QAA6B;QACpD,MAAM,kBAAkB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;QACjF,OAAO,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC;IAC1C,CAAC;IAEO,YAAY,CAAC,QAA6B;QAC9C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QACrC,MAAM,UAAU,GAAoB,EAAE,CAAC;QAEvC,IAAI,CAAC;YACD,IAAI,QAAQ,CAAC,UAAU,KAAK,KAAK,IAAI,QAAQ,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;gBAClE,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,EAAE,CAAC;oBAC9C,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAC/D,CAAC;YACL,CAAC;iBAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,iBAAiB,IAAI,QAAQ,CAAC,UAAU,KAAK,iBAAiB,EAAE,CAAC;gBAChG,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC,EAAE,CAAC;oBAC9C,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAC/D,CAAC;gBACD,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,EAAE,CAAC;oBAClD,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACtE,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,0BAA0B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,gBAAgB,CAAC,SAAwB;QAC7C,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC1B,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,EACtE,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAC5F,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,CACpC,KAAK,EACL,SAAS,CAAC,OAAO,EACjB,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,CACvC,CAAC;QAEF,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC;QACjC,UAAU,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;QAEjC,6CAA6C;QAC7C,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACvB,UAAU,CAAC,kBAAkB,GAAG;gBAC5B,IAAI,MAAM,CAAC,4BAA4B,CACnC,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAC/C,oBAAoB,SAAS,CAAC,UAAU,WAAW,CACtD;aACJ,CAAC;QACN,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEO,WAAW,CAAC,QAAsC;QACtD,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,OAAO;gBACR,OAAO,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC3C,KAAK,SAAS;gBACV,OAAO,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC7C,KAAK,MAAM;gBACP,OAAO,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC;YACjD;gBACI,OAAO,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;QAC/C,CAAC;IACL,CAAC;IAEM,mBAAmB,CAAC,MAAqC;QAC5D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAEM,YAAY,CAAC,MAAoB;QACpC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;CACJ;AAvGD,gDAuGC"}