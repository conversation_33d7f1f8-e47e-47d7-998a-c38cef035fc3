{"version": 3, "file": "code-action-provider.js", "sourceRoot": "", "sources": ["../../src/providers/code-action-provider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,MAAa,kBAAkB;IAI3B,YAAY,MAAoB,EAAE,cAAsB;QACpD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACzC,CAAC;IAEM,kBAAkB,CACrB,QAA6B,EAC7B,KAAsC,EACtC,OAAiC,EACjC,KAA+B;QAE/B,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,6CAA6C;QAC7C,MAAM,oBAAoB,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,CACnD,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,KAAK,YAAY,CACnD,CAAC;QAEF,KAAK,MAAM,UAAU,IAAI,oBAAoB,EAAE,CAAC;YAC5C,MAAM,SAAS,GAAG,IAAI,CAAC,8BAA8B,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAC5E,IAAI,SAAS,EAAE,CAAC;gBACZ,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,6BAA6B,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;YACzF,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,8BAA8B,CAClC,QAA6B,EAC7B,UAA6B;QAE7B,oDAAoD;QACpD,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;QACnC,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QACrC,IAAI,UAAU,GAAoB,EAAE,CAAC;QAErC,IAAI,CAAC;YACD,IAAI,QAAQ,CAAC,UAAU,KAAK,KAAK,IAAI,QAAQ,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;gBAClE,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACxD,CAAC;iBAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,iBAAiB,IAAI,QAAQ,CAAC,UAAU,KAAK,iBAAiB,EAAE,CAAC;gBAChG,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;gBAC3D,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;YACtE,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;YACpE,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,kDAAkD;QAClD,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,qBAAqB;QACnE,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC;QAEhD,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACvB,CAAC,CAAC,IAAI,KAAK,IAAI;YACf,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,iCAAiC;YACrE,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,CACnC,IAAI,IAAI,CAAC;IACd,CAAC;IAEO,6BAA6B,CACjC,QAA6B,EAC7B,UAA6B,EAC7B,SAAwB;QAExB,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,uCAAuC;QACvC,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;YAC3F,IAAI,cAAc,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QAED,wDAAwD;QACxD,IAAI,SAAS,CAAC,IAAI,KAAK,WAAW,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;YAC1D,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;YAClF,IAAI,cAAc,EAAE,CAAC;gBACjB,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QAED,iEAAiE;QACjE,IAAI,SAAS,CAAC,IAAI,KAAK,WAAW,IAAI,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YAC/E,MAAM,uBAAuB,GAAG,IAAI,CAAC,6BAA6B,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YACzF,IAAI,uBAAuB,EAAE,CAAC;gBAC1B,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YAC1C,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,oBAAoB,CACxB,QAA6B,EAC7B,UAA6B,EAC7B,QAAkB;QAElB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,gBAAgB,QAAQ,CAAC,OAAO,EAAE,EAClC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;QAEF,MAAM,CAAC,WAAW,GAAG,CAAC,UAAU,CAAC,CAAC;QAClC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;QAE1B,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;QAExC,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;QACnE,CAAC;aAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YACjC,oEAAoE;YACpE,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;YACzC,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;YAC5C,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAEvE,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;gBAChB,MAAM,cAAc,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACzD,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;YAChE,CAAC;QACL,CAAC;aAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACpC,oCAAoC;YACpC,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,oBAAoB,CACxB,QAA6B,EAC7B,UAA6B,EAC7B,SAAwB;QAExB,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACjD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,QAAQ,KAAK,YAAY,SAAS,CAAC,IAAI,QAAQ,EAC/C,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;QAEF,MAAM,CAAC,WAAW,GAAG,CAAC,UAAU,CAAC,CAAC;QAElC,8BAA8B;QAC9B,MAAM,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,oBAAoB;YAC7B,KAAK,EAAE,WAAW;YAClB,SAAS,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC;SACrC,CAAC;QAEF,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,6BAA6B,CACjC,QAA6B,EAC7B,UAA6B;QAE7B,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,qCAAqC,EACrC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;QAEF,MAAM,CAAC,WAAW,GAAG,CAAC,UAAU,CAAC,CAAC;QAElC,uCAAuC;QACvC,MAAM,CAAC,OAAO,GAAG;YACb,OAAO,EAAE,8BAA8B;YACvC,KAAK,EAAE,qBAAqB;SAC/B,CAAC;QAEF,OAAO,MAAM,CAAC;IAClB,CAAC;IAEM,YAAY,CAAC,MAAoB;QACpC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;IAEM,oBAAoB,CAAC,cAAsB;QAC9C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACzC,CAAC;CACJ;AAzLD,gDAyLC"}