"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagnosticProvider = void 0;
const vscode = __importStar(require("vscode"));
class DiagnosticProvider {
    constructor(linter, config) {
        this.linter = linter;
        this.config = config;
    }
    updateDiagnostics(document, collection) {
        if (!this.shouldLintDocument(document)) {
            collection.delete(document.uri);
            return;
        }
        const violations = this.lintDocument(document);
        const diagnostics = violations.map(violation => this.createDiagnostic(violation));
        collection.set(document.uri, diagnostics);
    }
    shouldLintDocument(document) {
        const supportedLanguages = ['css', 'scss', 'javascriptreact', 'typescriptreact'];
        return supportedLanguages.includes(document.languageId) &&
            document.uri.scheme === 'file';
    }
    lintDocument(document) {
        const content = document.getText();
        const filePath = document.uri.fsPath;
        const violations = [];
        try {
            if (document.languageId === 'css' || document.languageId === 'scss') {
                if (this.config.get('enableTokenLinting', true)) {
                    violations.push(...this.linter.lintCss(content, filePath));
                }
            }
            else if (document.languageId === 'javascriptreact' || document.languageId === 'typescriptreact') {
                if (this.config.get('enableTokenLinting', true)) {
                    violations.push(...this.linter.lintJsx(content, filePath));
                }
                if (this.config.get('enableComponentLinting', true)) {
                    violations.push(...this.linter.lintComponents(content, filePath));
                }
            }
        }
        catch (error) {
            console.error(`Error linting document ${filePath}:`, error);
        }
        return violations;
    }
    createDiagnostic(violation) {
        const range = new vscode.Range(new vscode.Position(Math.max(0, violation.line - 1), violation.column), new vscode.Position(Math.max(0, violation.line - 1), violation.column + violation.length));
        const diagnostic = new vscode.Diagnostic(range, violation.message, this.mapSeverity(violation.severity));
        diagnostic.source = 'AI-Stethic';
        diagnostic.code = violation.type;
        // Add related information for better context
        if (violation.suggestion) {
            diagnostic.relatedInformation = [
                new vscode.DiagnosticRelatedInformation(new vscode.Location(vscode.Uri.file(''), range), `Suggestion: Use "${violation.suggestion}" instead`)
            ];
        }
        return diagnostic;
    }
    mapSeverity(severity) {
        switch (severity) {
            case 'error':
                return vscode.DiagnosticSeverity.Error;
            case 'warning':
                return vscode.DiagnosticSeverity.Warning;
            case 'info':
                return vscode.DiagnosticSeverity.Information;
            default:
                return vscode.DiagnosticSeverity.Error;
        }
    }
    updateConfiguration(config) {
        this.config = config;
    }
    updateLinter(linter) {
        this.linter = linter;
    }
}
exports.DiagnosticProvider = DiagnosticProvider;
//# sourceMappingURL=diagnostic-provider.js.map