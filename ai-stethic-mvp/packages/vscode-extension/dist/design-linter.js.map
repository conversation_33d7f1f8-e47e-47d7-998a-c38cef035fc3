{"version": 3, "file": "design-linter.js", "sourceRoot": "", "sources": ["../src/design-linter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2DAA2D;AAC3D,uCAAyB;AACzB,2CAA6B;AAE7B,MAAM,UAAU,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;AAClD,4DAAmC;AAiFnC,MAAa,YAAY;IAIvB,YAAY,cAAuB,EAAE,UAAuB,EAAE;QAC5D,IAAI,CAAC,OAAO,GAAG;YACb,kBAAkB,EAAE,IAAI;YACxB,sBAAsB,EAAE,IAAI;YAC5B,cAAc,EAAE,EAAE;YAClB,gBAAgB,EAAE,CAAC;YACnB,GAAG,OAAO;SACX,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;IACxD,CAAC;IAEM,OAAO,CAAC,OAAe,EAAE,QAAgB;QAC9C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB;YAAE,OAAO,EAAE,CAAC;QAEhD,MAAM,UAAU,GAAoB,EAAE,CAAC;QAEvC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;YAErD,IAAI,CAAC,SAAS,CAAC,CAAC,IAAS,EAAE,EAAE;gBAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;gBACpC,IAAI,CAAC,QAAQ;oBAAE,OAAO;gBAEtB,MAAM,IAAI,GAAG;oBACX,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,MAAM,EAAE,QAAQ,CAAC,MAAM;iBACxB,CAAC;gBAEF,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAC3D,IAAI,SAAS,EAAE,CAAC;oBACd,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC7B,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,4BAA4B,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEM,OAAO,CAAC,OAAe,EAAE,QAAgB;QAC9C,qDAAqD;QACrD,8DAA8D;QAC9D,OAAO,EAAE,CAAC;IACZ,CAAC;IAEM,cAAc,CAAC,OAAe,EAAE,QAAgB;QACrD,+BAA+B;QAC/B,OAAO,EAAE,CAAC;IACZ,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,aAAqB;QAMvD,MAAM,UAAU,GAAG;YACjB,MAAM,EAAE,EAAsB;YAC9B,OAAO,EAAE,EAAsB;YAC/B,UAAU,EAAE,EAAsB;YAClC,YAAY,EAAE,EAAsB;SACrC,CAAC;QAEF,4DAA4D;QAC5D,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC;YACrB,KAAK,EAAE,SAAS;YAChB,IAAI,EAAE,OAAO;YACb,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAC1D,aAAa,EAAE,SAAS;SACzB,CAAC,CAAC;QAEH,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;YACtB,KAAK,EAAE,MAAM;YACb,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAC1D,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAEM,KAAK,CAAC,0BAA0B,CAAC,aAAqB,EAAE,eAAuB,mBAAmB;QACvG,MAAM,UAAU,GAA0B,EAAE,CAAC;QAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;QAE7D,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;YAClC,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,gCAAgC;QAChC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;YAC5C,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;oBACzD,IAAI,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACzC,UAAU,CAAC,IAAI,CAAC;4BACd,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE;4BAChD,aAAa,EAAE,EAAE;4BACjB,eAAe,EAAE,EAAE;yBACpB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEM,gBAAgB,CACrB,UAKC,EACD,aAAoC,EAAE;QAEtC,MAAM,UAAU,GAAe;YAC7B,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,UAAU,EAAE,EAAE;YACd,YAAY,EAAE,EAAE;YAChB,UAAU,EAAE,EAAE;SACf,CAAC;QAEF,+BAA+B;QAC/B,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACpC,MAAM,KAAK,GAAG,IAAA,oBAAS,EAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACzC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpB,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC;oBACrB,IAAI,EAAE,SAAS,CAAC,aAAa,IAAI,SAAS,CAAC,KAAK;oBAChD,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,IAAI,EAAE,OAAO;oBACb,GAAG,EAAE,KAAK,CAAC,WAAW,EAAE;iBACzB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACrC,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACtE,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,MAAM,GAAG,QAAQ,CAAC;gBACtB,IAAI,IAAI,KAAK,KAAK;oBAAE,MAAM,IAAI,EAAE,CAAC;gBACjC,IAAI,IAAI,KAAK,IAAI;oBAAE,MAAM,IAAI,EAAE,CAAC;gBAEhC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;oBACtB,IAAI,EAAE,SAAS,CAAC,aAAa,IAAI,SAAS,CAAC,KAAK;oBAChD,KAAK,EAAE,SAAS,CAAC,KAAK;oBACtB,IAAI,EAAE,SAAS;oBACf,MAAM;oBACN,IAAI,EAAE,IAAiC;iBACxC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,iBAAiB;QACjB,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACxB,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAEM,cAAc,CAAC,UAAsB,EAAE,QAAgB;QAC5D,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QACnC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACxB,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACzC,CAAC;QAED,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAEM,cAAc,CAAC,KAAsE,EAAE,cAAsB;QAClH,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QAEvD,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,OAAO;gBACV,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAmB,CAAC,CAAC;gBAC5C,MAAM;YACR,KAAK,SAAS;gBACZ,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAqB,CAAC,CAAC;gBAC/C,MAAM;YACR,KAAK,YAAY;gBACf,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,KAAwB,CAAC,CAAC;gBACrD,MAAM;YACR,KAAK,cAAc;gBACjB,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,KAA0B,CAAC,CAAC;gBACzD,MAAM;QACV,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAEO,cAAc,CAAC,cAAuB;QAC5C,MAAM,iBAAiB,GAAe;YACpC,MAAM,EAAE,EAAE;YACV,OAAO,EAAE,EAAE;YACX,UAAU,EAAE,EAAE;YACd,YAAY,EAAE,EAAE;YAChB,UAAU,EAAE,EAAE;SACf,CAAC;QAEF,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;YACtD,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YACzD,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAEnC,OAAO;gBACL,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,EAAE;gBAC3B,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,EAAE;gBAC7B,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE;gBACnC,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI,EAAE;gBACvC,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE;aACpC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,cAAc,GAAG,EAAE,KAAK,CAAC,CAAC;YACrE,OAAO,iBAAiB,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,IAAS,EAAE,QAAgB;QACrD,yBAAyB;QACzB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,IAAA,oBAAS,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;gBAAE,OAAO,IAAI,CAAC;YAElC,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YAErC,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CACxD,IAAA,oBAAS,EAAC,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,KAAK,QAAQ,CAChD,CAAC;YAEF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO;oBACL,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,WAAW,IAAI,CAAC,KAAK,iBAAiB;oBAC/C,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,MAAM,EAAE,IAAI,CAAC,MAAM;oBACnB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;oBACzB,QAAQ,EAAE,OAAO;oBACjB,QAAQ,EAAE;wBACR,IAAI,EAAE,SAAS;wBACf,OAAO,EAAE,IAAI,CAAC,KAAK;qBACpB;iBACF,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,eAAe,CAAC,QAAgB;QACtC,OAAO,CAAC,OAAO,EAAE,kBAAkB,EAAE,cAAc,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC5F,CAAC;CACF;AAhRD,oCAgRC"}