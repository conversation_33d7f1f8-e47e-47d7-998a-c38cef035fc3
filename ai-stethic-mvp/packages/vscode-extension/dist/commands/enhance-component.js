"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhanceComponentCommand = void 0;
const vscode = __importStar(require("vscode"));
const axios_1 = __importDefault(require("axios"));
class EnhanceComponentCommand {
    async execute() {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found. Please open a file and select some code to enhance.');
            return;
        }
        const selection = editor.selection;
        if (selection.isEmpty) {
            vscode.window.showErrorMessage('Please select some code to enhance.');
            return;
        }
        const selectedCode = editor.document.getText(selection);
        if (!selectedCode.trim()) {
            vscode.window.showErrorMessage('Please select some code to enhance.');
            return;
        }
        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "AI-Stethic: Enhancing Component",
                cancellable: true
            }, async (progress, token) => {
                progress.report({ increment: 0, message: "Getting style hint..." });
                // Get optional style hint from user
                const styleHint = await vscode.window.showInputBox({
                    prompt: 'Enter an optional style hint (e.g., "make it more modern", "add dark mode support")',
                    placeHolder: 'Optional: describe the styling you want...'
                });
                if (token.isCancellationRequested)
                    return;
                progress.report({ increment: 30, message: "Sending to enhancement API..." });
                // Get API URL from configuration
                const config = vscode.workspace.getConfiguration('aiStethic');
                const apiUrl = config.get('enhanceApiUrl', 'http://localhost:8000/enhance');
                try {
                    // Send request to enhancement API
                    const response = await axios_1.default.post(apiUrl, {
                        code: selectedCode,
                        styleHint: styleHint || ''
                    }, {
                        timeout: 10000, // 10 second timeout
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    if (token.isCancellationRequested)
                        return;
                    progress.report({ increment: 80, message: "Applying enhancements..." });
                    const enhancedCode = response.data.enhancedCode;
                    if (!enhancedCode) {
                        throw new Error('No enhanced code received from API');
                    }
                    // Replace selected code with enhanced version
                    await editor.edit(editBuilder => {
                        editBuilder.replace(selection, enhancedCode);
                    });
                    progress.report({ increment: 100, message: "Complete!" });
                    vscode.window.showInformationMessage('AI-Stethic: Component enhanced successfully!', 'View Changes').then(action => {
                        if (action === 'View Changes') {
                            // Show diff or highlight changes
                            vscode.commands.executeCommand('workbench.action.compareEditor.previousChange');
                        }
                    });
                }
                catch (apiError) {
                    if (axios_1.default.isAxiosError(apiError)) {
                        if (apiError.code === 'ECONNREFUSED') {
                            vscode.window.showErrorMessage('AI-Stethic: Enhancement API is not running. Please start the stub backend server.', 'Show Instructions').then(action => {
                                if (action === 'Show Instructions') {
                                    this.showBackendInstructions();
                                }
                            });
                        }
                        else if (apiError.response?.status === 404) {
                            vscode.window.showErrorMessage('AI-Stethic: Enhancement endpoint not found. Please check the API URL in settings.');
                        }
                        else {
                            vscode.window.showErrorMessage(`AI-Stethic: API error - ${apiError.message}`);
                        }
                    }
                    else {
                        throw apiError; // Re-throw non-axios errors
                    }
                }
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`AI-Stethic: Enhancement failed - ${error}`);
        }
    }
    showBackendInstructions() {
        const message = `To use the AI-Stethic enhancement feature, you need to start the stub backend server:

1. Open a terminal in your project root
2. Navigate to the stub-backend directory:
   cd stub-backend
3. Install dependencies:
   npm install
4. Start the server:
   npm start

The server will run on http://localhost:8000 by default.`;
        vscode.window.showInformationMessage(message, { modal: true }, 'Copy Commands').then(action => {
            if (action === 'Copy Commands') {
                vscode.env.clipboard.writeText('cd stub-backend && npm install && npm start');
                vscode.window.showInformationMessage('Commands copied to clipboard!');
            }
        });
    }
    async enhanceSelection(code, styleHint) {
        try {
            const config = vscode.workspace.getConfiguration('aiStethic');
            const apiUrl = config.get('enhanceApiUrl', 'http://localhost:8000/enhance');
            const response = await axios_1.default.post(apiUrl, {
                code,
                styleHint: styleHint || ''
            }, {
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            return response.data.enhancedCode || null;
        }
        catch (error) {
            console.error('Enhancement API error:', error);
            return null;
        }
    }
}
exports.EnhanceComponentCommand = EnhanceComponentCommand;
//# sourceMappingURL=enhance-component.js.map