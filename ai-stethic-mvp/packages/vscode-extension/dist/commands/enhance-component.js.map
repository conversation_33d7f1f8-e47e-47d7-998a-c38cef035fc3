{"version": 3, "file": "enhance-component.js", "sourceRoot": "", "sources": ["../../src/commands/enhance-component.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,kDAA0B;AAE1B,MAAa,uBAAuB;IACzB,KAAK,CAAC,OAAO;QAChB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6EAA6E,CAAC,CAAC;YAC9G,OAAO;QACX,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QACnC,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACpB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qCAAqC,CAAC,CAAC;YACtE,OAAO;QACX,CAAC;QAED,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACxD,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qCAAqC,CAAC,CAAC;YACtE,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,iCAAiC;gBACxC,WAAW,EAAE,IAAI;aACpB,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACzB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;gBAEpE,oCAAoC;gBACpC,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBAC/C,MAAM,EAAE,qFAAqF;oBAC7F,WAAW,EAAE,4CAA4C;iBAC5D,CAAC,CAAC;gBAEH,IAAI,KAAK,CAAC,uBAAuB;oBAAE,OAAO;gBAE1C,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC,CAAC;gBAE7E,iCAAiC;gBACjC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;gBAC9D,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,+BAA+B,CAAC,CAAC;gBAE5E,IAAI,CAAC;oBACD,kCAAkC;oBAClC,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,MAAM,EAAE;wBACtC,IAAI,EAAE,YAAY;wBAClB,SAAS,EAAE,SAAS,IAAI,EAAE;qBAC7B,EAAE;wBACC,OAAO,EAAE,KAAK,EAAE,oBAAoB;wBACpC,OAAO,EAAE;4BACL,cAAc,EAAE,kBAAkB;yBACrC;qBACJ,CAAC,CAAC;oBAEH,IAAI,KAAK,CAAC,uBAAuB;wBAAE,OAAO;oBAE1C,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC,CAAC;oBAExE,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC;oBAChD,IAAI,CAAC,YAAY,EAAE,CAAC;wBAChB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;oBAC1D,CAAC;oBAED,8CAA8C;oBAC9C,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;wBAC5B,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;oBACjD,CAAC,CAAC,CAAC;oBAEH,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;oBAE1D,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,8CAA8C,EAC9C,cAAc,CACjB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;wBACZ,IAAI,MAAM,KAAK,cAAc,EAAE,CAAC;4BAC5B,iCAAiC;4BACjC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+CAA+C,CAAC,CAAC;wBACpF,CAAC;oBACL,CAAC,CAAC,CAAC;gBAEP,CAAC;gBAAC,OAAO,QAAQ,EAAE,CAAC;oBAChB,IAAI,eAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC/B,IAAI,QAAQ,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;4BACnC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC1B,mFAAmF,EACnF,mBAAmB,CACtB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gCACZ,IAAI,MAAM,KAAK,mBAAmB,EAAE,CAAC;oCACjC,IAAI,CAAC,uBAAuB,EAAE,CAAC;gCACnC,CAAC;4BACL,CAAC,CAAC,CAAC;wBACP,CAAC;6BAAM,IAAI,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;4BAC3C,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC1B,mFAAmF,CACtF,CAAC;wBACN,CAAC;6BAAM,CAAC;4BACJ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC1B,2BAA2B,QAAQ,CAAC,OAAO,EAAE,CAChD,CAAC;wBACN,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,MAAM,QAAQ,CAAC,CAAC,4BAA4B;oBAChD,CAAC;gBACL,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;QAChF,CAAC;IACL,CAAC;IAEO,uBAAuB;QAC3B,MAAM,OAAO,GAAG;;;;;;;;;;yDAUiC,CAAC;QAElD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,OAAO,EACP,EAAE,KAAK,EAAE,IAAI,EAAE,EACf,eAAe,CAClB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACZ,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;gBAC7B,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,6CAA6C,CAAC,CAAC;gBAC9E,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;YAC1E,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,IAAY,EAAE,SAAkB;QAC1D,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;YAC9D,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,+BAA+B,CAAC,CAAC;YAE5E,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAAC,MAAM,EAAE;gBACtC,IAAI;gBACJ,SAAS,EAAE,SAAS,IAAI,EAAE;aAC7B,EAAE;gBACC,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE;oBACL,cAAc,EAAE,kBAAkB;iBACrC;aACJ,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,OAAO,IAAI,CAAC;QAChB,CAAC;IACL,CAAC;CACJ;AA5JD,0DA4JC"}