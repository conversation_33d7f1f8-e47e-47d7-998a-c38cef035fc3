"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
function activate(context) {
    console.log('🎨 AI-Stethic extension is now active!');
    // Register commands
    const initializeTokensCommand = vscode.commands.registerCommand('aiStethic.initializeTokens', async () => {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('AI-Stethic requires a workspace to be opened.');
            return;
        }
        try {
            // Simple token initialization
            const fs = require('fs');
            const tokensDir = path.join(workspaceRoot, '.ai-stethic');
            const tokensFile = path.join(tokensDir, 'tokens.json');
            if (!fs.existsSync(tokensDir)) {
                fs.mkdirSync(tokensDir, { recursive: true });
            }
            const defaultTokens = {
                colors: [
                    { name: "primary", value: "#3b82f6", type: "color", hex: "#3b82f6" },
                    { name: "secondary", value: "#64748b", type: "color", hex: "#64748b" }
                ],
                spacing: [
                    { name: "sm", value: "8px", type: "spacing", pixels: 8, unit: "px" },
                    { name: "md", value: "16px", type: "spacing", pixels: 16, unit: "px" }
                ],
                typography: [
                    { name: "heading", value: "Inter", type: "typography", fontFamily: "Inter" }
                ],
                borderRadius: [
                    { name: "rounded", value: "4px", type: "borderRadius", pixels: 4, unit: "px" }
                ],
                components: {}
            };
            fs.writeFileSync(tokensFile, JSON.stringify(defaultTokens, null, 2));
            vscode.window.showInformationMessage('AI-Stethic: Design tokens initialized successfully!');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to initialize tokens: ${error}`);
        }
    });
    context.subscriptions.push(initializeTokensCommand);
    const registerComponentsCommand = vscode.commands.registerCommand('aiStethic.registerComponents', async () => {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('AI-Stethic requires a workspace to be opened.');
            return;
        }
        try {
            const componentDir = path.join(workspaceRoot, 'src', 'components', 'ui');
            const fs = require('fs');
            if (!fs.existsSync(componentDir)) {
                vscode.window.showWarningMessage(`Component directory not found: ${componentDir}`);
                return;
            }
            const files = fs.readdirSync(componentDir);
            const components = files
                .filter((file) => file.endsWith('.tsx') || file.endsWith('.jsx'))
                .map((file) => {
                const name = path.basename(file, path.extname(file));
                return {
                    name,
                    importPath: `./src/components/ui/${file}`,
                    requiredProps: [],
                    requiredClasses: []
                };
            });
            vscode.window.showInformationMessage(`Found ${components.length} components: ${components.map((c) => c.name).join(', ')}`);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Failed to scan components: ${error}`);
        }
    });
    context.subscriptions.push(registerComponentsCommand);
    const enhanceComponentCommand = vscode.commands.registerCommand('aiStethic.enhanceComponent', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found.');
            return;
        }
        const selection = editor.selection;
        if (selection.isEmpty) {
            vscode.window.showErrorMessage('Please select some code to enhance.');
            return;
        }
        const selectedCode = editor.document.getText(selection);
        const styleHint = await vscode.window.showInputBox({
            prompt: 'Enter an optional style hint',
            placeHolder: 'e.g., "make it modern", "add dark mode"'
        });
        try {
            const axios = require('axios');
            const response = await axios.post('http://localhost:8000/enhance', {
                code: selectedCode,
                styleHint: styleHint || ''
            }, { timeout: 5000 });
            const enhancedCode = response.data.enhancedCode;
            if (enhancedCode) {
                await editor.edit(editBuilder => {
                    editBuilder.replace(selection, enhancedCode);
                });
                vscode.window.showInformationMessage('AI-Stethic: Component enhanced successfully!');
            }
        }
        catch (error) {
            if (error.code === 'ECONNREFUSED') {
                vscode.window.showErrorMessage('AI-Stethic: Enhancement API is not running. Please start the stub backend server.');
            }
            else {
                vscode.window.showErrorMessage(`Enhancement failed: ${error.message}`);
            }
        }
    });
    context.subscriptions.push(enhanceComponentCommand);
    // Add basic linting functionality
    const diagnosticCollection = vscode.languages.createDiagnosticCollection('ai-stethic');
    context.subscriptions.push(diagnosticCollection);
    // Register document change listener for linting
    const documentChangeListener = vscode.workspace.onDidSaveTextDocument((document) => {
        updateDiagnostics(document, diagnosticCollection);
    });
    context.subscriptions.push(documentChangeListener);
    // Lint all open documents initially
    vscode.workspace.textDocuments.forEach(doc => {
        updateDiagnostics(doc, diagnosticCollection);
    });
    // Show welcome message
    vscode.window.showInformationMessage('AI-Stethic is ready! Use the command palette to get started.', 'Initialize Tokens').then(selection => {
        if (selection === 'Initialize Tokens') {
            vscode.commands.executeCommand('aiStethic.initializeTokens');
        }
    });
}
function updateDiagnostics(document, collection) {
    if (!shouldLintDocument(document)) {
        collection.delete(document.uri);
        return;
    }
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot)
        return;
    const tokensFilePath = path.join(workspaceRoot, '.ai-stethic', 'tokens.json');
    const fs = require('fs');
    if (!fs.existsSync(tokensFilePath)) {
        collection.delete(document.uri);
        return;
    }
    try {
        const tokensContent = fs.readFileSync(tokensFilePath, 'utf-8');
        const tokens = JSON.parse(tokensContent);
        const diagnostics = [];
        const content = document.getText();
        const lines = content.split('\n');
        // Simple color detection for CSS/SCSS files
        if (document.languageId === 'css' || document.languageId === 'scss') {
            lines.forEach((line, lineIndex) => {
                const colorMatches = line.match(/#[0-9a-fA-F]{3,6}/g);
                if (colorMatches) {
                    colorMatches.forEach(color => {
                        const isValidToken = tokens.colors.some((token) => token.hex.toLowerCase() === color.toLowerCase());
                        if (!isValidToken) {
                            const startPos = line.indexOf(color);
                            const range = new vscode.Range(lineIndex, startPos, lineIndex, startPos + color.length);
                            const diagnostic = new vscode.Diagnostic(range, `Color "${color}" is not in your design tokens. Consider using a token instead.`, vscode.DiagnosticSeverity.Warning);
                            diagnostic.source = 'AI-Stethic';
                            diagnostics.push(diagnostic);
                        }
                    });
                }
                // Simple spacing detection
                const spacingMatches = line.match(/\b(\d+)px\b/g);
                if (spacingMatches) {
                    spacingMatches.forEach(spacing => {
                        const pixels = parseInt(spacing.replace('px', ''));
                        const isValidToken = tokens.spacing.some((token) => token.pixels === pixels);
                        if (!isValidToken) {
                            const startPos = line.indexOf(spacing);
                            const range = new vscode.Range(lineIndex, startPos, lineIndex, startPos + spacing.length);
                            const diagnostic = new vscode.Diagnostic(range, `Spacing "${spacing}" is not in your design tokens. Consider using a token instead.`, vscode.DiagnosticSeverity.Warning);
                            diagnostic.source = 'AI-Stethic';
                            diagnostics.push(diagnostic);
                        }
                    });
                }
            });
        }
        collection.set(document.uri, diagnostics);
    }
    catch (error) {
        console.error('Error linting document:', error);
        collection.delete(document.uri);
    }
}
function shouldLintDocument(document) {
    const supportedLanguages = ['css', 'scss', 'javascriptreact', 'typescriptreact'];
    return supportedLanguages.includes(document.languageId) &&
        document.uri.scheme === 'file';
}
function deactivate() {
    console.log('AI-Stethic extension is now deactivated.');
}
//# sourceMappingURL=extension.js.map