"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const design_linter_1 = require("./design-linter");
const diagnostic_provider_1 = require("./providers/diagnostic-provider");
const code_action_provider_1 = require("./providers/code-action-provider");
const token_wizard_1 = require("./wizards/token-wizard");
const component_wizard_1 = require("./wizards/component-wizard");
const enhance_component_1 = require("./commands/enhance-component");
let diagnosticProvider;
let codeActionProvider;
function activate(context) {
    console.log('AI-Stethic extension is now active!');
    // Initialize providers
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot) {
        vscode.window.showErrorMessage('AI-Stethic requires a workspace to be opened.');
        return;
    }
    // Get configuration
    const config = vscode.workspace.getConfiguration('aiStethic');
    const tokensFilePath = path.join(workspaceRoot, config.get('tokensFile', '.ai-stethic/tokens.json'));
    // Initialize design linter
    const linter = new design_linter_1.DesignLinter(tokensFilePath, {
        enableTokenLinting: config.get('enableTokenLinting', true),
        enableComponentLinting: config.get('enableComponentLinting', true),
        colorThreshold: config.get('colorThreshold', 10),
        spacingThreshold: config.get('spacingThreshold', 2),
    });
    // Initialize providers
    diagnosticProvider = new diagnostic_provider_1.DiagnosticProvider(linter, config);
    codeActionProvider = new code_action_provider_1.CodeActionProvider(linter, tokensFilePath);
    // Register diagnostic provider
    const diagnosticCollection = vscode.languages.createDiagnosticCollection('ai-stethic');
    context.subscriptions.push(diagnosticCollection);
    // Register document change listeners
    const documentChangeListener = vscode.workspace.onDidSaveTextDocument((document) => {
        if (config.get('enableLinter', true)) {
            diagnosticProvider.updateDiagnostics(document, diagnosticCollection);
        }
    });
    context.subscriptions.push(documentChangeListener);
    // Register code action provider
    const codeActionDisposable = vscode.languages.registerCodeActionsProvider([
        { scheme: 'file', language: 'css' },
        { scheme: 'file', language: 'scss' },
        { scheme: 'file', language: 'javascriptreact' },
        { scheme: 'file', language: 'typescriptreact' }
    ], codeActionProvider, {
        providedCodeActionKinds: [vscode.CodeActionKind.QuickFix]
    });
    context.subscriptions.push(codeActionDisposable);
    // Register commands
    const initializeTokensCommand = vscode.commands.registerCommand('aiStethic.initializeTokens', async () => {
        const wizard = new token_wizard_1.TokenWizard(linter, workspaceRoot);
        await wizard.run();
        // Refresh diagnostics after token initialization
        vscode.workspace.textDocuments.forEach(doc => {
            diagnosticProvider.updateDiagnostics(doc, diagnosticCollection);
        });
    });
    context.subscriptions.push(initializeTokensCommand);
    const registerComponentsCommand = vscode.commands.registerCommand('aiStethic.registerComponents', async () => {
        const wizard = new component_wizard_1.ComponentWizard(linter, workspaceRoot);
        await wizard.run();
        // Refresh diagnostics after component registration
        vscode.workspace.textDocuments.forEach(doc => {
            diagnosticProvider.updateDiagnostics(doc, diagnosticCollection);
        });
    });
    context.subscriptions.push(registerComponentsCommand);
    const enhanceComponentCommand = vscode.commands.registerCommand('aiStethic.enhanceComponent', async () => {
        const enhancer = new enhance_component_1.EnhanceComponentCommand();
        await enhancer.execute();
    });
    context.subscriptions.push(enhanceComponentCommand);
    const addTokenCommand = vscode.commands.registerCommand('aiStethic.addToken', async () => {
        await addNewToken(linter, tokensFilePath, diagnosticCollection);
    });
    context.subscriptions.push(addTokenCommand);
    // Check if tokens file exists, if not, prompt to initialize
    const fs = require('fs');
    if (!fs.existsSync(tokensFilePath)) {
        vscode.window.showInformationMessage('AI-Stethic: No design tokens found. Would you like to initialize them?', 'Initialize Tokens', 'Later').then(selection => {
            if (selection === 'Initialize Tokens') {
                vscode.commands.executeCommand('aiStethic.initializeTokens');
            }
        });
    }
    else {
        // Run initial diagnostics on all open documents
        vscode.workspace.textDocuments.forEach(doc => {
            if (config.get('enableLinter', true)) {
                diagnosticProvider.updateDiagnostics(doc, diagnosticCollection);
            }
        });
    }
    // Listen for configuration changes
    const configChangeListener = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('aiStethic')) {
            // Reload the extension with new configuration
            const newConfig = vscode.workspace.getConfiguration('aiStethic');
            const newTokensFilePath = path.join(workspaceRoot, newConfig.get('tokensFile', '.ai-stethic/tokens.json'));
            // Update linter configuration
            const newLinter = new design_linter_1.DesignLinter(newTokensFilePath, {
                enableTokenLinting: newConfig.get('enableTokenLinting', true),
                enableComponentLinting: newConfig.get('enableComponentLinting', true),
                colorThreshold: newConfig.get('colorThreshold', 10),
                spacingThreshold: newConfig.get('spacingThreshold', 2),
            });
            // Update providers
            diagnosticProvider = new diagnostic_provider_1.DiagnosticProvider(newLinter, newConfig);
            codeActionProvider = new code_action_provider_1.CodeActionProvider(newLinter, newTokensFilePath);
            // Refresh diagnostics
            if (newConfig.get('enableLinter', true)) {
                vscode.workspace.textDocuments.forEach(doc => {
                    diagnosticProvider.updateDiagnostics(doc, diagnosticCollection);
                });
            }
            else {
                diagnosticCollection.clear();
            }
        }
    });
    context.subscriptions.push(configChangeListener);
}
async function addNewToken(linter, tokensFilePath, diagnosticCollection) {
    // Get the current selection or word under cursor
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor found.');
        return;
    }
    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);
    if (!selectedText) {
        vscode.window.showErrorMessage('Please select a value to add as a token.');
        return;
    }
    // Determine token type
    const tokenType = await vscode.window.showQuickPick([
        { label: 'Color', value: 'color' },
        { label: 'Spacing', value: 'spacing' },
        { label: 'Typography', value: 'typography' },
        { label: 'Border Radius', value: 'borderRadius' }
    ], {
        placeHolder: 'Select token type'
    });
    if (!tokenType)
        return;
    // Get token name
    const tokenName = await vscode.window.showInputBox({
        prompt: `Enter name for ${tokenType.label.toLowerCase()} token`,
        placeHolder: 'e.g., primary, large, heading'
    });
    if (!tokenName)
        return;
    try {
        // Create token object based on type
        let token = {
            name: tokenName,
            value: selectedText,
            type: tokenType.value
        };
        // Add type-specific properties
        if (tokenType.value === 'color') {
            const tinycolor = require('tinycolor2');
            const color = tinycolor(selectedText);
            if (color.isValid()) {
                token.hex = color.toHexString();
                token.rgb = color.toRgb();
                token.hsl = color.toHsl();
            }
        }
        else if (tokenType.value === 'spacing' || tokenType.value === 'borderRadius') {
            const match = selectedText.match(/^(\d+(?:\.\d+)?)(px|rem|em)$/);
            if (match) {
                const numValue = parseFloat(match[1]);
                const unit = match[2];
                let pixels = numValue;
                if (unit === 'rem')
                    pixels *= 16;
                if (unit === 'em')
                    pixels *= 16;
                token.pixels = pixels;
                token.unit = unit;
            }
        }
        // Add token to file
        linter.addTokenToFile(token, tokensFilePath);
        vscode.window.showInformationMessage(`Added ${tokenType.label.toLowerCase()} token "${tokenName}"`);
        // Refresh diagnostics
        vscode.workspace.textDocuments.forEach(doc => {
            diagnosticProvider.updateDiagnostics(doc, diagnosticCollection);
        });
    }
    catch (error) {
        vscode.window.showErrorMessage(`Failed to add token: ${error}`);
    }
}
function deactivate() {
    console.log('AI-Stethic extension is now deactivated.');
}
//# sourceMappingURL=extension.js.map