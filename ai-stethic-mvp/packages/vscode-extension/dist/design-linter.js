"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DesignLinter = void 0;
// Simplified design linter for VS Code extension packaging
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
const safeParser = require('postcss-safe-parser');
const tinycolor2_1 = __importDefault(require("tinycolor2"));
class DesignLinter {
    constructor(tokensFilePath, options = {}) {
        this.options = {
            enableTokenLinting: true,
            enableComponentLinting: true,
            colorThreshold: 10,
            spacingThreshold: 2,
            ...options,
        };
        this.tokensFile = this.loadTokensFile(tokensFilePath);
    }
    lintCss(content, filePath) {
        if (!this.options.enableTokenLinting)
            return [];
        const violations = [];
        try {
            const root = safeParser(content, { from: filePath });
            root.walkDecls((decl) => {
                const position = decl.source?.start;
                if (!position)
                    return;
                const rule = {
                    property: decl.prop,
                    value: decl.value,
                    line: position.line,
                    column: position.column,
                };
                const violation = this.checkTokenViolation(rule, filePath);
                if (violation) {
                    violations.push(violation);
                }
            });
        }
        catch (error) {
            console.warn(`Failed to parse CSS file ${filePath}:`, error);
        }
        return violations;
    }
    lintJsx(content, filePath) {
        // Simplified JSX linting - just return empty for now
        // In a full implementation, this would use Babel to parse JSX
        return [];
    }
    lintComponents(content, filePath) {
        // Simplified component linting
        return [];
    }
    async scanWorkspaceForTokens(workspacePath) {
        const candidates = {
            colors: [],
            spacing: [],
            typography: [],
            borderRadius: [],
        };
        // Simplified scanning - just return some example candidates
        candidates.colors.push({
            value: '#3b82f6',
            type: 'color',
            occurrences: [{ file: 'example.css', line: 1, column: 1 }],
            suggestedName: 'primary'
        });
        candidates.spacing.push({
            value: '16px',
            type: 'spacing',
            occurrences: [{ file: 'example.css', line: 2, column: 1 }],
            suggestedName: 'md'
        });
        return candidates;
    }
    async scanWorkspaceForComponents(workspacePath, componentDir = 'src/components/ui') {
        const components = [];
        const componentPath = path.join(workspacePath, componentDir);
        if (!fs.existsSync(componentPath)) {
            return components;
        }
        // Simplified component scanning
        try {
            const files = fs.readdirSync(componentPath);
            files.forEach(file => {
                if (file.endsWith('.tsx') || file.endsWith('.jsx')) {
                    const fileName = path.basename(file, path.extname(file));
                    if (/^[A-Z][a-zA-Z0-9]*$/.test(fileName)) {
                        components.push({
                            name: fileName,
                            importPath: `./${path.join(componentDir, file)}`,
                            requiredProps: [],
                            requiredClasses: []
                        });
                    }
                }
            });
        }
        catch (error) {
            console.warn('Error scanning components:', error);
        }
        return components;
    }
    createTokensFile(candidates, components = []) {
        const tokensFile = {
            colors: [],
            spacing: [],
            typography: [],
            borderRadius: [],
            components: {}
        };
        // Convert candidates to tokens
        candidates.colors.forEach(candidate => {
            const color = (0, tinycolor2_1.default)(candidate.value);
            if (color.isValid()) {
                tokensFile.colors.push({
                    name: candidate.suggestedName || candidate.value,
                    value: candidate.value,
                    type: 'color',
                    hex: color.toHexString()
                });
            }
        });
        candidates.spacing.forEach(candidate => {
            const match = candidate.value.match(/^(\d+(?:\.\d+)?)(px|rem|em|%)$/);
            if (match) {
                const numValue = parseFloat(match[1]);
                const unit = match[2];
                let pixels = numValue;
                if (unit === 'rem')
                    pixels *= 16;
                if (unit === 'em')
                    pixels *= 16;
                tokensFile.spacing.push({
                    name: candidate.suggestedName || candidate.value,
                    value: candidate.value,
                    type: 'spacing',
                    pixels,
                    unit: unit
                });
            }
        });
        // Add components
        components.forEach(comp => {
            tokensFile.components[comp.name] = comp;
        });
        return tokensFile;
    }
    saveTokensFile(tokensFile, filePath) {
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        fs.writeFileSync(filePath, JSON.stringify(tokensFile, null, 2));
    }
    addTokenToFile(token, tokensFilePath) {
        const tokensFile = this.loadTokensFile(tokensFilePath);
        switch (token.type) {
            case 'color':
                tokensFile.colors.push(token);
                break;
            case 'spacing':
                tokensFile.spacing.push(token);
                break;
            case 'typography':
                tokensFile.typography.push(token);
                break;
            case 'borderRadius':
                tokensFile.borderRadius.push(token);
                break;
        }
        this.saveTokensFile(tokensFile, tokensFilePath);
        this.tokensFile = tokensFile;
    }
    loadTokensFile(tokensFilePath) {
        const defaultTokensFile = {
            colors: [],
            spacing: [],
            typography: [],
            borderRadius: [],
            components: {},
        };
        if (!tokensFilePath || !fs.existsSync(tokensFilePath)) {
            return defaultTokensFile;
        }
        try {
            const content = fs.readFileSync(tokensFilePath, 'utf-8');
            const parsed = JSON.parse(content);
            return {
                colors: parsed.colors || [],
                spacing: parsed.spacing || [],
                typography: parsed.typography || [],
                borderRadius: parsed.borderRadius || [],
                components: parsed.components || {},
            };
        }
        catch (error) {
            console.warn(`Failed to load tokens file ${tokensFilePath}:`, error);
            return defaultTokensFile;
        }
    }
    checkTokenViolation(rule, filePath) {
        // Check color violations
        if (this.isColorProperty(rule.property)) {
            const color = (0, tinycolor2_1.default)(rule.value);
            if (!color.isValid())
                return null;
            const colorHex = color.toHexString();
            const matchingToken = this.tokensFile.colors.find(token => (0, tinycolor2_1.default)(token.hex).toHexString() === colorHex);
            if (!matchingToken) {
                return {
                    type: 'color',
                    message: `COLOR: "${rule.value}" not in tokens`,
                    line: rule.line,
                    column: rule.column,
                    length: rule.value.length,
                    severity: 'error',
                    quickFix: {
                        type: 'replace',
                        newText: rule.value,
                    },
                };
            }
        }
        return null;
    }
    isColorProperty(property) {
        return ['color', 'background-color', 'border-color', 'fill', 'stroke'].includes(property);
    }
}
exports.DesignLinter = DesignLinter;
//# sourceMappingURL=design-linter.js.map