import * as vscode from 'vscode';
import { DesignLinter, LintViolation } from '@ai-stethic/design-linter';

export class DiagnosticProvider {
    private linter: DesignLinter;
    private config: vscode.WorkspaceConfiguration;

    constructor(linter: DesignLinter, config: vscode.WorkspaceConfiguration) {
        this.linter = linter;
        this.config = config;
    }

    public updateDiagnostics(
        document: vscode.TextDocument,
        collection: vscode.DiagnosticCollection
    ): void {
        if (!this.shouldLintDocument(document)) {
            collection.delete(document.uri);
            return;
        }

        const violations = this.lintDocument(document);
        const diagnostics = violations.map(violation => this.createDiagnostic(violation));
        
        collection.set(document.uri, diagnostics);
    }

    private shouldLintDocument(document: vscode.TextDocument): boolean {
        const supportedLanguages = ['css', 'scss', 'javascriptreact', 'typescriptreact'];
        return supportedLanguages.includes(document.languageId) && 
               document.uri.scheme === 'file';
    }

    private lintDocument(document: vscode.TextDocument): LintViolation[] {
        const content = document.getText();
        const filePath = document.uri.fsPath;
        const violations: LintViolation[] = [];

        try {
            if (document.languageId === 'css' || document.languageId === 'scss') {
                if (this.config.get('enableTokenLinting', true)) {
                    violations.push(...this.linter.lintCss(content, filePath));
                }
            } else if (document.languageId === 'javascriptreact' || document.languageId === 'typescriptreact') {
                if (this.config.get('enableTokenLinting', true)) {
                    violations.push(...this.linter.lintJsx(content, filePath));
                }
                if (this.config.get('enableComponentLinting', true)) {
                    violations.push(...this.linter.lintComponents(content, filePath));
                }
            }
        } catch (error) {
            console.error(`Error linting document ${filePath}:`, error);
        }

        return violations;
    }

    private createDiagnostic(violation: LintViolation): vscode.Diagnostic {
        const range = new vscode.Range(
            new vscode.Position(Math.max(0, violation.line - 1), violation.column),
            new vscode.Position(Math.max(0, violation.line - 1), violation.column + violation.length)
        );

        const diagnostic = new vscode.Diagnostic(
            range,
            violation.message,
            this.mapSeverity(violation.severity)
        );

        diagnostic.source = 'AI-Stethic';
        diagnostic.code = violation.type;

        // Add related information for better context
        if (violation.suggestion) {
            diagnostic.relatedInformation = [
                new vscode.DiagnosticRelatedInformation(
                    new vscode.Location(vscode.Uri.file(''), range),
                    `Suggestion: Use "${violation.suggestion}" instead`
                )
            ];
        }

        return diagnostic;
    }

    private mapSeverity(severity: 'error' | 'warning' | 'info'): vscode.DiagnosticSeverity {
        switch (severity) {
            case 'error':
                return vscode.DiagnosticSeverity.Error;
            case 'warning':
                return vscode.DiagnosticSeverity.Warning;
            case 'info':
                return vscode.DiagnosticSeverity.Information;
            default:
                return vscode.DiagnosticSeverity.Error;
        }
    }

    public updateConfiguration(config: vscode.WorkspaceConfiguration): void {
        this.config = config;
    }

    public updateLinter(linter: DesignLinter): void {
        this.linter = linter;
    }
}
