import * as vscode from 'vscode';
import { DesignLinter, LintViolation, QuickFix } from '@ai-stethic/design-linter';

export class CodeActionProvider implements vscode.CodeActionProvider {
    private linter: DesignLinter;
    private tokensFilePath: string;

    constructor(linter: DesignLinter, tokensFilePath: string) {
        this.linter = linter;
        this.tokensFilePath = tokensFilePath;
    }

    public provideCodeActions(
        document: vscode.TextDocument,
        range: vscode.Range | vscode.Selection,
        context: vscode.CodeActionContext,
        token: vscode.CancellationToken
    ): vscode.ProviderResult<(vscode.CodeAction | vscode.Command)[]> {
        const actions: vscode.CodeAction[] = [];

        // Filter diagnostics to only AI-Stethic ones
        const aiStethicDiagnostics = context.diagnostics.filter(
            diagnostic => diagnostic.source === 'AI-Stethic'
        );

        for (const diagnostic of aiStethicDiagnostics) {
            const violation = this.extractViolationFromDiagnostic(document, diagnostic);
            if (violation) {
                actions.push(...this.createCodeActionsForViolation(document, diagnostic, violation));
            }
        }

        return actions;
    }

    private extractViolationFromDiagnostic(
        document: vscode.TextDocument,
        diagnostic: vscode.Diagnostic
    ): LintViolation | null {
        // Re-lint the document to get the violation details
        const content = document.getText();
        const filePath = document.uri.fsPath;
        let violations: LintViolation[] = [];

        try {
            if (document.languageId === 'css' || document.languageId === 'scss') {
                violations = this.linter.lintCss(content, filePath);
            } else if (document.languageId === 'javascriptreact' || document.languageId === 'typescriptreact') {
                violations.push(...this.linter.lintJsx(content, filePath));
                violations.push(...this.linter.lintComponents(content, filePath));
            }
        } catch (error) {
            console.error('Error re-linting document for code actions:', error);
            return null;
        }

        // Find the violation that matches this diagnostic
        const line = diagnostic.range.start.line + 1; // Convert to 1-based
        const column = diagnostic.range.start.character;

        return violations.find(v => 
            v.line === line && 
            Math.abs(v.column - column) <= 2 && // Allow small column differences
            v.message === diagnostic.message
        ) || null;
    }

    private createCodeActionsForViolation(
        document: vscode.TextDocument,
        diagnostic: vscode.Diagnostic,
        violation: LintViolation
    ): vscode.CodeAction[] {
        const actions: vscode.CodeAction[] = [];

        // Create quick fix action if available
        if (violation.quickFix) {
            const quickFixAction = this.createQuickFixAction(document, diagnostic, violation.quickFix);
            if (quickFixAction) {
                actions.push(quickFixAction);
            }
        }

        // Create "Add as new token" action for token violations
        if (violation.type !== 'component' && !violation.suggestion) {
            const addTokenAction = this.createAddTokenAction(document, diagnostic, violation);
            if (addTokenAction) {
                actions.push(addTokenAction);
            }
        }

        // Create "Register component" action for unregistered components
        if (violation.type === 'component' && violation.message.includes('not imported')) {
            const registerComponentAction = this.createRegisterComponentAction(document, diagnostic);
            if (registerComponentAction) {
                actions.push(registerComponentAction);
            }
        }

        return actions;
    }

    private createQuickFixAction(
        document: vscode.TextDocument,
        diagnostic: vscode.Diagnostic,
        quickFix: QuickFix
    ): vscode.CodeAction | null {
        const action = new vscode.CodeAction(
            `Replace with ${quickFix.newText}`,
            vscode.CodeActionKind.QuickFix
        );

        action.diagnostics = [diagnostic];
        action.isPreferred = true;

        const edit = new vscode.WorkspaceEdit();
        
        if (quickFix.type === 'replace') {
            edit.replace(document.uri, diagnostic.range, quickFix.newText);
        } else if (quickFix.type === 'add') {
            // For adding props or classes, insert at the end of the opening tag
            const line = diagnostic.range.start.line;
            const lineText = document.lineAt(line).text;
            const tagEnd = lineText.indexOf('>', diagnostic.range.start.character);
            
            if (tagEnd !== -1) {
                const insertPosition = new vscode.Position(line, tagEnd);
                edit.insert(document.uri, insertPosition, quickFix.newText);
            }
        } else if (quickFix.type === 'import') {
            // Add import at the top of the file
            const firstLine = new vscode.Position(0, 0);
            edit.insert(document.uri, firstLine, quickFix.newText + '\n');
        }

        action.edit = edit;
        return action;
    }

    private createAddTokenAction(
        document: vscode.TextDocument,
        diagnostic: vscode.Diagnostic,
        violation: LintViolation
    ): vscode.CodeAction | null {
        const value = document.getText(diagnostic.range);
        const action = new vscode.CodeAction(
            `Add "${value}" as new ${violation.type} token`,
            vscode.CodeActionKind.QuickFix
        );

        action.diagnostics = [diagnostic];
        
        // Create command to add token
        action.command = {
            command: 'aiStethic.addToken',
            title: 'Add Token',
            arguments: [value, violation.type]
        };

        return action;
    }

    private createRegisterComponentAction(
        document: vscode.TextDocument,
        diagnostic: vscode.Diagnostic
    ): vscode.CodeAction | null {
        const action = new vscode.CodeAction(
            'Register component in design system',
            vscode.CodeActionKind.QuickFix
        );

        action.diagnostics = [diagnostic];
        
        // Create command to register component
        action.command = {
            command: 'aiStethic.registerComponents',
            title: 'Register Components'
        };

        return action;
    }

    public updateLinter(linter: DesignLinter): void {
        this.linter = linter;
    }

    public updateTokensFilePath(tokensFilePath: string): void {
        this.tokensFilePath = tokensFilePath;
    }
}
