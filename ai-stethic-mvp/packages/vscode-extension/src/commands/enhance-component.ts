import * as vscode from 'vscode';
import axios from 'axios';

export class EnhanceComponentCommand {
    public async execute(): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found. Please open a file and select some code to enhance.');
            return;
        }

        const selection = editor.selection;
        if (selection.isEmpty) {
            vscode.window.showErrorMessage('Please select some code to enhance.');
            return;
        }

        const selectedCode = editor.document.getText(selection);
        if (!selectedCode.trim()) {
            vscode.window.showErrorMessage('Please select some code to enhance.');
            return;
        }

        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "AI-Stethic: Enhancing Component",
                cancellable: true
            }, async (progress, token) => {
                progress.report({ increment: 0, message: "Getting style hint..." });

                // Get optional style hint from user
                const styleHint = await vscode.window.showInputBox({
                    prompt: 'Enter an optional style hint (e.g., "make it more modern", "add dark mode support")',
                    placeHolder: 'Optional: describe the styling you want...'
                });

                if (token.isCancellationRequested) return;

                progress.report({ increment: 30, message: "Sending to enhancement API..." });

                // Get API URL from configuration
                const config = vscode.workspace.getConfiguration('aiStethic');
                const apiUrl = config.get('enhanceApiUrl', 'http://localhost:8000/enhance');

                try {
                    // Send request to enhancement API
                    const response = await axios.post(apiUrl, {
                        code: selectedCode,
                        styleHint: styleHint || ''
                    }, {
                        timeout: 10000, // 10 second timeout
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (token.isCancellationRequested) return;

                    progress.report({ increment: 80, message: "Applying enhancements..." });

                    const enhancedCode = response.data.enhancedCode;
                    if (!enhancedCode) {
                        throw new Error('No enhanced code received from API');
                    }

                    // Replace selected code with enhanced version
                    await editor.edit(editBuilder => {
                        editBuilder.replace(selection, enhancedCode);
                    });

                    progress.report({ increment: 100, message: "Complete!" });

                    vscode.window.showInformationMessage(
                        'AI-Stethic: Component enhanced successfully!',
                        'View Changes'
                    ).then(action => {
                        if (action === 'View Changes') {
                            // Show diff or highlight changes
                            vscode.commands.executeCommand('workbench.action.compareEditor.previousChange');
                        }
                    });

                } catch (apiError) {
                    if (axios.isAxiosError(apiError)) {
                        if (apiError.code === 'ECONNREFUSED') {
                            vscode.window.showErrorMessage(
                                'AI-Stethic: Enhancement API is not running. Please start the stub backend server.',
                                'Show Instructions'
                            ).then(action => {
                                if (action === 'Show Instructions') {
                                    this.showBackendInstructions();
                                }
                            });
                        } else if (apiError.response?.status === 404) {
                            vscode.window.showErrorMessage(
                                'AI-Stethic: Enhancement endpoint not found. Please check the API URL in settings.'
                            );
                        } else {
                            vscode.window.showErrorMessage(
                                `AI-Stethic: API error - ${apiError.message}`
                            );
                        }
                    } else {
                        throw apiError; // Re-throw non-axios errors
                    }
                }
            });
        } catch (error) {
            vscode.window.showErrorMessage(`AI-Stethic: Enhancement failed - ${error}`);
        }
    }

    private showBackendInstructions(): void {
        const message = `To use the AI-Stethic enhancement feature, you need to start the stub backend server:

1. Open a terminal in your project root
2. Navigate to the stub-backend directory:
   cd stub-backend
3. Install dependencies:
   npm install
4. Start the server:
   npm start

The server will run on http://localhost:8000 by default.`;

        vscode.window.showInformationMessage(
            message,
            { modal: true },
            'Copy Commands'
        ).then(action => {
            if (action === 'Copy Commands') {
                vscode.env.clipboard.writeText('cd stub-backend && npm install && npm start');
                vscode.window.showInformationMessage('Commands copied to clipboard!');
            }
        });
    }

    public async enhanceSelection(code: string, styleHint?: string): Promise<string | null> {
        try {
            const config = vscode.workspace.getConfiguration('aiStethic');
            const apiUrl = config.get('enhanceApiUrl', 'http://localhost:8000/enhance');

            const response = await axios.post(apiUrl, {
                code,
                styleHint: styleHint || ''
            }, {
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            return response.data.enhancedCode || null;
        } catch (error) {
            console.error('Enhancement API error:', error);
            return null;
        }
    }
}
