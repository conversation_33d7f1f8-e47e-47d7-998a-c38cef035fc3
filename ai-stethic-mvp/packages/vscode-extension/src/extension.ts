import * as vscode from 'vscode';
import * as path from 'path';
import { DesignLinter } from './design-linter';
import { DiagnosticProvider } from './providers/diagnostic-provider';
import { CodeActionProvider } from './providers/code-action-provider';
import { TokenWizard } from './wizards/token-wizard';
import { ComponentWizard } from './wizards/component-wizard';
import { EnhanceComponentCommand } from './commands/enhance-component';

let diagnosticProvider: DiagnosticProvider;
let codeActionProvider: CodeActionProvider;

export function activate(context: vscode.ExtensionContext) {
    console.log('AI-Stethic extension is now active!');

    // Register commands first (they should always be available)
    registerCommands(context);

    // Initialize providers
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    if (!workspaceRoot) {
        console.log('AI-Stethic: No workspace opened, commands registered but linting disabled.');
        return;
    }

    // Get configuration
    const config = vscode.workspace.getConfiguration('aiStethic');
    const tokensFilePath = path.join(workspaceRoot, config.get('tokensFile', '.ai-stethic/tokens.json'));

    // Initialize design linter
    const linter = new DesignLinter(tokensFilePath, {
        enableTokenLinting: config.get('enableTokenLinting', true),
        enableComponentLinting: config.get('enableComponentLinting', true),
        colorThreshold: config.get('colorThreshold', 10),
        spacingThreshold: config.get('spacingThreshold', 2),
    });

    // Initialize providers
    diagnosticProvider = new DiagnosticProvider(linter, config);
    codeActionProvider = new CodeActionProvider(linter, tokensFilePath);

    // Register diagnostic provider
    const diagnosticCollection = vscode.languages.createDiagnosticCollection('ai-stethic');
    context.subscriptions.push(diagnosticCollection);

    // Register document change listeners
    const documentChangeListener = vscode.workspace.onDidSaveTextDocument((document) => {
        if (config.get('enableLinter', true)) {
            diagnosticProvider.updateDiagnostics(document, diagnosticCollection);
        }
    });
    context.subscriptions.push(documentChangeListener);

    // Register code action provider
    const codeActionDisposable = vscode.languages.registerCodeActionsProvider(
        [
            { scheme: 'file', language: 'css' },
            { scheme: 'file', language: 'scss' },
            { scheme: 'file', language: 'javascriptreact' },
            { scheme: 'file', language: 'typescriptreact' }
        ],
        codeActionProvider,
        {
            providedCodeActionKinds: [vscode.CodeActionKind.QuickFix]
        }
    );
    context.subscriptions.push(codeActionDisposable);

    // Register workspace-dependent commands
    registerWorkspaceCommands(context, linter, workspaceRoot, diagnosticCollection);

    // Check if tokens file exists, if not, prompt to initialize
    const fs = require('fs');
    if (!fs.existsSync(tokensFilePath)) {
        vscode.window.showInformationMessage(
            'AI-Stethic: No design tokens found. Would you like to initialize them?',
            'Initialize Tokens',
            'Later'
        ).then(selection => {
            if (selection === 'Initialize Tokens') {
                vscode.commands.executeCommand('aiStethic.initializeTokens');
            }
        });
    } else {
        // Run initial diagnostics on all open documents
        vscode.workspace.textDocuments.forEach(doc => {
            if (config.get('enableLinter', true)) {
                diagnosticProvider.updateDiagnostics(doc, diagnosticCollection);
            }
        });
    }

    // Listen for configuration changes
    const configChangeListener = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('aiStethic')) {
            // Reload the extension with new configuration
            const newConfig = vscode.workspace.getConfiguration('aiStethic');
            const newTokensFilePath = path.join(workspaceRoot, newConfig.get('tokensFile', '.ai-stethic/tokens.json'));
            
            // Update linter configuration
            const newLinter = new DesignLinter(newTokensFilePath, {
                enableTokenLinting: newConfig.get('enableTokenLinting', true),
                enableComponentLinting: newConfig.get('enableComponentLinting', true),
                colorThreshold: newConfig.get('colorThreshold', 10),
                spacingThreshold: newConfig.get('spacingThreshold', 2),
            });

            // Update providers
            diagnosticProvider = new DiagnosticProvider(newLinter, newConfig);
            codeActionProvider = new CodeActionProvider(newLinter, newTokensFilePath);

            // Refresh diagnostics
            if (newConfig.get('enableLinter', true)) {
                vscode.workspace.textDocuments.forEach(doc => {
                    diagnosticProvider.updateDiagnostics(doc, diagnosticCollection);
                });
            } else {
                diagnosticCollection.clear();
            }
        }
    });
    context.subscriptions.push(configChangeListener);
}

async function addNewToken(
    linter: DesignLinter, 
    tokensFilePath: string, 
    diagnosticCollection: vscode.DiagnosticCollection
) {
    // Get the current selection or word under cursor
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor found.');
        return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);
    
    if (!selectedText) {
        vscode.window.showErrorMessage('Please select a value to add as a token.');
        return;
    }

    // Determine token type
    const tokenType = await vscode.window.showQuickPick([
        { label: 'Color', value: 'color' },
        { label: 'Spacing', value: 'spacing' },
        { label: 'Typography', value: 'typography' },
        { label: 'Border Radius', value: 'borderRadius' }
    ], {
        placeHolder: 'Select token type'
    });

    if (!tokenType) return;

    // Get token name
    const tokenName = await vscode.window.showInputBox({
        prompt: `Enter name for ${tokenType.label.toLowerCase()} token`,
        placeHolder: 'e.g., primary, large, heading'
    });

    if (!tokenName) return;

    try {
        // Create token object based on type
        let token: any = {
            name: tokenName,
            value: selectedText,
            type: tokenType.value
        };

        // Add type-specific properties
        if (tokenType.value === 'color') {
            const tinycolor = require('tinycolor2');
            const color = tinycolor(selectedText);
            if (color.isValid()) {
                token.hex = color.toHexString();
                token.rgb = color.toRgb();
                token.hsl = color.toHsl();
            }
        } else if (tokenType.value === 'spacing' || tokenType.value === 'borderRadius') {
            const match = selectedText.match(/^(\d+(?:\.\d+)?)(px|rem|em)$/);
            if (match) {
                const numValue = parseFloat(match[1]);
                const unit = match[2];
                let pixels = numValue;
                if (unit === 'rem') pixels *= 16;
                if (unit === 'em') pixels *= 16;
                
                token.pixels = pixels;
                token.unit = unit;
            }
        }

        // Add token to file
        linter.addTokenToFile(token, tokensFilePath);
        
        vscode.window.showInformationMessage(`Added ${tokenType.label.toLowerCase()} token "${tokenName}"`);
        
        // Refresh diagnostics
        vscode.workspace.textDocuments.forEach(doc => {
            diagnosticProvider.updateDiagnostics(doc, diagnosticCollection);
        });
        
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to add token: ${error}`);
    }
}

function registerCommands(context: vscode.ExtensionContext) {
    // Register basic commands that don't require workspace
    const enhanceComponentCommand = vscode.commands.registerCommand('aiStethic.enhanceComponent', async () => {
        const enhancer = new EnhanceComponentCommand();
        await enhancer.execute();
    });
    context.subscriptions.push(enhanceComponentCommand);

    // Register workspace-dependent commands with workspace check
    const initializeTokensCommand = vscode.commands.registerCommand('aiStethic.initializeTokens', async () => {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('AI-Stethic requires a workspace to be opened.');
            return;
        }

        const config = vscode.workspace.getConfiguration('aiStethic');
        const tokensFilePath = path.join(workspaceRoot, config.get('tokensFile', '.ai-stethic/tokens.json'));
        const linter = new DesignLinter(tokensFilePath);

        const wizard = new TokenWizard(linter, workspaceRoot);
        await wizard.run();
    });
    context.subscriptions.push(initializeTokensCommand);

    const registerComponentsCommand = vscode.commands.registerCommand('aiStethic.registerComponents', async () => {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('AI-Stethic requires a workspace to be opened.');
            return;
        }

        const config = vscode.workspace.getConfiguration('aiStethic');
        const tokensFilePath = path.join(workspaceRoot, config.get('tokensFile', '.ai-stethic/tokens.json'));
        const linter = new DesignLinter(tokensFilePath);

        const wizard = new ComponentWizard(linter, workspaceRoot);
        await wizard.run();
    });
    context.subscriptions.push(registerComponentsCommand);

    const addTokenCommand = vscode.commands.registerCommand('aiStethic.addToken', async () => {
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('AI-Stethic requires a workspace to be opened.');
            return;
        }

        const config = vscode.workspace.getConfiguration('aiStethic');
        const tokensFilePath = path.join(workspaceRoot, config.get('tokensFile', '.ai-stethic/tokens.json'));
        const linter = new DesignLinter(tokensFilePath);

        await addNewToken(linter, tokensFilePath, vscode.languages.createDiagnosticCollection('ai-stethic'));
    });
    context.subscriptions.push(addTokenCommand);
}

function registerWorkspaceCommands(
    context: vscode.ExtensionContext,
    linter: DesignLinter,
    workspaceRoot: string,
    diagnosticCollection: vscode.DiagnosticCollection
) {
    // These are already registered in registerCommands, but we can add refresh logic here
    // This function is called only when workspace is available
}

export function deactivate() {
    console.log('AI-Stethic extension is now deactivated.');
}
