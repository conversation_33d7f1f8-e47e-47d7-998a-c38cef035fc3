// Simplified design linter for VS Code extension packaging
import * as fs from 'fs';
import * as path from 'path';
import postcss from 'postcss';
const safeParser = require('postcss-safe-parser');
import tinycolor from 'tinycolor2';

export interface DesignToken {
  name: string;
  value: string;
  type: 'color' | 'spacing' | 'typography' | 'borderRadius';
}

export interface ColorToken extends DesignToken {
  type: 'color';
  hex: string;
}

export interface SpacingToken extends DesignToken {
  type: 'spacing';
  pixels: number;
  unit: 'px' | 'rem' | 'em' | '%';
}

export interface TypographyToken extends DesignToken {
  type: 'typography';
  fontFamily?: string;
  fontSize?: string;
}

export interface BorderRadiusToken extends DesignToken {
  type: 'borderRadius';
  pixels: number;
  unit: 'px' | 'rem' | 'em';
}

export interface ComponentDefinition {
  name: string;
  importPath: string;
  requiredProps?: string[];
  requiredClasses?: string[];
}

export interface TokensFile {
  colors: ColorToken[];
  spacing: SpacingToken[];
  typography: TypographyToken[];
  borderRadius: BorderRadiusToken[];
  components: Record<string, ComponentDefinition>;
}

export interface LintViolation {
  type: 'color' | 'spacing' | 'typography' | 'borderRadius' | 'component';
  message: string;
  line: number;
  column: number;
  length: number;
  severity: 'error' | 'warning' | 'info';
  suggestion?: string;
  quickFix?: QuickFix;
}

export interface QuickFix {
  type: 'replace' | 'add' | 'remove' | 'import';
  newText: string;
}

export interface LintOptions {
  tokensFile?: string;
  enableTokenLinting?: boolean;
  enableComponentLinting?: boolean;
  colorThreshold?: number;
  spacingThreshold?: number;
}

export interface TokenCandidate {
  value: string;
  type: 'color' | 'spacing' | 'typography' | 'borderRadius';
  occurrences: Array<{
    file: string;
    line: number;
    column: number;
  }>;
  suggestedName?: string;
}

export class DesignLinter {
  private tokensFile: TokensFile;
  private options: LintOptions;

  constructor(tokensFilePath?: string, options: LintOptions = {}) {
    this.options = {
      enableTokenLinting: true,
      enableComponentLinting: true,
      colorThreshold: 10,
      spacingThreshold: 2,
      ...options,
    };

    this.tokensFile = this.loadTokensFile(tokensFilePath);
  }

  public lintCss(content: string, filePath: string): LintViolation[] {
    if (!this.options.enableTokenLinting) return [];
    
    const violations: LintViolation[] = [];
    
    try {
      const root = safeParser(content, { from: filePath });

      root.walkDecls((decl: any) => {
        const position = decl.source?.start;
        if (!position) return;

        const rule = {
          property: decl.prop,
          value: decl.value,
          line: position.line,
          column: position.column,
        };

        const violation = this.checkTokenViolation(rule, filePath);
        if (violation) {
          violations.push(violation);
        }
      });
    } catch (error) {
      console.warn(`Failed to parse CSS file ${filePath}:`, error);
    }

    return violations;
  }

  public lintJsx(content: string, filePath: string): LintViolation[] {
    // Simplified JSX linting - just return empty for now
    // In a full implementation, this would use Babel to parse JSX
    return [];
  }

  public lintComponents(content: string, filePath: string): LintViolation[] {
    // Simplified component linting
    return [];
  }

  public async scanWorkspaceForTokens(workspacePath: string): Promise<{
    colors: TokenCandidate[];
    spacing: TokenCandidate[];
    typography: TokenCandidate[];
    borderRadius: TokenCandidate[];
  }> {
    const candidates = {
      colors: [] as TokenCandidate[],
      spacing: [] as TokenCandidate[],
      typography: [] as TokenCandidate[],
      borderRadius: [] as TokenCandidate[],
    };

    // Simplified scanning - just return some example candidates
    candidates.colors.push({
      value: '#3b82f6',
      type: 'color',
      occurrences: [{ file: 'example.css', line: 1, column: 1 }],
      suggestedName: 'primary'
    });

    candidates.spacing.push({
      value: '16px',
      type: 'spacing',
      occurrences: [{ file: 'example.css', line: 2, column: 1 }],
      suggestedName: 'md'
    });

    return candidates;
  }

  public async scanWorkspaceForComponents(workspacePath: string, componentDir: string = 'src/components/ui'): Promise<ComponentDefinition[]> {
    const components: ComponentDefinition[] = [];
    const componentPath = path.join(workspacePath, componentDir);

    if (!fs.existsSync(componentPath)) {
      return components;
    }

    // Simplified component scanning
    try {
      const files = fs.readdirSync(componentPath);
      files.forEach(file => {
        if (file.endsWith('.tsx') || file.endsWith('.jsx')) {
          const fileName = path.basename(file, path.extname(file));
          if (/^[A-Z][a-zA-Z0-9]*$/.test(fileName)) {
            components.push({
              name: fileName,
              importPath: `./${path.join(componentDir, file)}`,
              requiredProps: [],
              requiredClasses: []
            });
          }
        }
      });
    } catch (error) {
      console.warn('Error scanning components:', error);
    }

    return components;
  }

  public createTokensFile(
    candidates: {
      colors: TokenCandidate[];
      spacing: TokenCandidate[];
      typography: TokenCandidate[];
      borderRadius: TokenCandidate[];
    },
    components: ComponentDefinition[] = []
  ): TokensFile {
    const tokensFile: TokensFile = {
      colors: [],
      spacing: [],
      typography: [],
      borderRadius: [],
      components: {}
    };

    // Convert candidates to tokens
    candidates.colors.forEach(candidate => {
      const color = tinycolor(candidate.value);
      if (color.isValid()) {
        tokensFile.colors.push({
          name: candidate.suggestedName || candidate.value,
          value: candidate.value,
          type: 'color',
          hex: color.toHexString()
        });
      }
    });

    candidates.spacing.forEach(candidate => {
      const match = candidate.value.match(/^(\d+(?:\.\d+)?)(px|rem|em|%)$/);
      if (match) {
        const numValue = parseFloat(match[1]);
        const unit = match[2];
        let pixels = numValue;
        if (unit === 'rem') pixels *= 16;
        if (unit === 'em') pixels *= 16;

        tokensFile.spacing.push({
          name: candidate.suggestedName || candidate.value,
          value: candidate.value,
          type: 'spacing',
          pixels,
          unit: unit as 'px' | 'rem' | 'em' | '%'
        });
      }
    });

    // Add components
    components.forEach(comp => {
      tokensFile.components[comp.name] = comp;
    });

    return tokensFile;
  }

  public saveTokensFile(tokensFile: TokensFile, filePath: string): void {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(filePath, JSON.stringify(tokensFile, null, 2));
  }

  public addTokenToFile(token: ColorToken | SpacingToken | TypographyToken | BorderRadiusToken, tokensFilePath: string): void {
    const tokensFile = this.loadTokensFile(tokensFilePath);
    
    switch (token.type) {
      case 'color':
        tokensFile.colors.push(token as ColorToken);
        break;
      case 'spacing':
        tokensFile.spacing.push(token as SpacingToken);
        break;
      case 'typography':
        tokensFile.typography.push(token as TypographyToken);
        break;
      case 'borderRadius':
        tokensFile.borderRadius.push(token as BorderRadiusToken);
        break;
    }

    this.saveTokensFile(tokensFile, tokensFilePath);
    this.tokensFile = tokensFile;
  }

  private loadTokensFile(tokensFilePath?: string): TokensFile {
    const defaultTokensFile: TokensFile = {
      colors: [],
      spacing: [],
      typography: [],
      borderRadius: [],
      components: {},
    };

    if (!tokensFilePath || !fs.existsSync(tokensFilePath)) {
      return defaultTokensFile;
    }

    try {
      const content = fs.readFileSync(tokensFilePath, 'utf-8');
      const parsed = JSON.parse(content);
      
      return {
        colors: parsed.colors || [],
        spacing: parsed.spacing || [],
        typography: parsed.typography || [],
        borderRadius: parsed.borderRadius || [],
        components: parsed.components || {},
      };
    } catch (error) {
      console.warn(`Failed to load tokens file ${tokensFilePath}:`, error);
      return defaultTokensFile;
    }
  }

  private checkTokenViolation(rule: any, filePath: string): LintViolation | null {
    // Check color violations
    if (this.isColorProperty(rule.property)) {
      const color = tinycolor(rule.value);
      if (!color.isValid()) return null;

      const colorHex = color.toHexString();
      
      const matchingToken = this.tokensFile.colors.find(token => 
        tinycolor(token.hex).toHexString() === colorHex
      );

      if (!matchingToken) {
        return {
          type: 'color',
          message: `COLOR: "${rule.value}" not in tokens`,
          line: rule.line,
          column: rule.column,
          length: rule.value.length,
          severity: 'error',
          quickFix: {
            type: 'replace',
            newText: rule.value,
          },
        };
      }
    }

    return null;
  }

  private isColorProperty(property: string): boolean {
    return ['color', 'background-color', 'border-color', 'fill', 'stroke'].includes(property);
  }
}
