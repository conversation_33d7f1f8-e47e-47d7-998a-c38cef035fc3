import * as vscode from 'vscode';
import * as path from 'path';
import { DesignLinter, ComponentDefinition, TokensFile } from '../design-linter';

export class ComponentWizard {
    private linter: DesignLinter;
    private workspaceRoot: string;

    constructor(linter: DesignLinter, workspaceRoot: string) {
        this.linter = linter;
        this.workspaceRoot = workspaceRoot;
    }

    public async run(): Promise<void> {
        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "AI-Stethic: Registering Components",
                cancellable: false
            }, async (progress) => {
                progress.report({ increment: 0, message: "Scanning for components..." });

                // Get component directory from configuration
                const config = vscode.workspace.getConfiguration('aiStethic');
                const componentDir = config.get('componentDirectory', 'src/components/ui');

                // Scan for components
                const components = await this.linter.scanWorkspaceForComponents(
                    this.workspaceRoot,
                    componentDir
                );

                if (components.length === 0) {
                    vscode.window.showInformationMessage(
                        `No React components found in ${componentDir}. Make sure your components are in the correct directory.`
                    );
                    return;
                }

                progress.report({ increment: 30, message: "Selecting components..." });

                // Let user select which components to register
                const selectedComponents = await this.selectComponents(components);
                if (!selectedComponents || selectedComponents.length === 0) {
                    return;
                }

                progress.report({ increment: 60, message: "Configuring components..." });

                // Configure each selected component
                const configuredComponents: ComponentDefinition[] = [];
                for (const component of selectedComponents) {
                    const configured = await this.configureComponent(component);
                    if (configured) {
                        configuredComponents.push(configured);
                    }
                }

                progress.report({ increment: 80, message: "Updating tokens file..." });

                // Update tokens file
                await this.updateTokensFile(configuredComponents);

                progress.report({ increment: 100, message: "Complete!" });

                vscode.window.showInformationMessage(
                    `AI-Stethic: Successfully registered ${configuredComponents.length} component(s)!`
                );
            });
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to register components: ${error}`);
        }
    }

    private async selectComponents(components: ComponentDefinition[]): Promise<ComponentDefinition[] | undefined> {
        const items = components.map(component => ({
            label: component.name,
            description: component.importPath,
            detail: 'React component',
            component
        }));

        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select components to register in your design system:',
            canPickMany: true,
            ignoreFocusOut: true
        });

        return selected?.map(item => item.component);
    }

    private async configureComponent(component: ComponentDefinition): Promise<ComponentDefinition | undefined> {
        vscode.window.showInformationMessage(`Configuring component: ${component.name}`);

        // Ask for required props
        const requiredPropsInput = await vscode.window.showInputBox({
            prompt: `Enter required props for ${component.name} (comma-separated)`,
            placeHolder: 'e.g., variant, size, disabled',
            value: component.requiredProps?.join(', ') || ''
        });

        const requiredProps = requiredPropsInput
            ? requiredPropsInput.split(',').map(prop => prop.trim()).filter(prop => prop.length > 0)
            : [];

        // Ask for required classes
        const requiredClassesInput = await vscode.window.showInputBox({
            prompt: `Enter required CSS classes for ${component.name} (comma-separated)`,
            placeHolder: 'e.g., btn, card, modal',
            value: component.requiredClasses?.join(', ') || ''
        });

        const requiredClasses = requiredClassesInput
            ? requiredClassesInput.split(',').map(cls => cls.trim()).filter(cls => cls.length > 0)
            : [];

        // Confirm import path
        const importPath = await vscode.window.showInputBox({
            prompt: `Confirm import path for ${component.name}`,
            value: component.importPath,
            validateInput: (value) => {
                if (!value || value.trim().length === 0) {
                    return 'Import path is required';
                }
                return null;
            }
        });

        if (!importPath) return undefined;

        return {
            name: component.name,
            importPath: importPath.trim(),
            requiredProps,
            requiredClasses
        };
    }

    private async updateTokensFile(components: ComponentDefinition[]): Promise<void> {
        const config = vscode.workspace.getConfiguration('aiStethic');
        const tokensFilePath = path.join(
            this.workspaceRoot,
            config.get('tokensFile', '.ai-stethic/tokens.json')
        );

        try {
            // Load existing tokens file
            const fs = require('fs');
            let tokensFile: TokensFile;

            if (fs.existsSync(tokensFilePath)) {
                const content = fs.readFileSync(tokensFilePath, 'utf-8');
                tokensFile = JSON.parse(content);
            } else {
                // Create new tokens file if it doesn't exist
                tokensFile = {
                    colors: [],
                    spacing: [],
                    typography: [],
                    borderRadius: [],
                    components: {}
                };
            }

            // Add components to tokens file
            components.forEach(component => {
                tokensFile.components[component.name] = component;
            });

            // Save updated tokens file
            this.linter.saveTokensFile(tokensFile, tokensFilePath);

        } catch (error) {
            throw new Error(`Failed to update tokens file: ${error}`);
        }
    }

    public async registerSingleComponent(componentName: string, importPath: string): Promise<void> {
        try {
            const component: ComponentDefinition = {
                name: componentName,
                importPath,
                requiredProps: [],
                requiredClasses: []
            };

            const configured = await this.configureComponent(component);
            if (configured) {
                await this.updateTokensFile([configured]);
                vscode.window.showInformationMessage(
                    `AI-Stethic: Successfully registered component "${componentName}"!`
                );
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to register component: ${error}`);
        }
    }
}
