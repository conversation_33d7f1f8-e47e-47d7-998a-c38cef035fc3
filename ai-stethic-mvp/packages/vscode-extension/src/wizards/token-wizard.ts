import * as vscode from 'vscode';
import * as path from 'path';
import { DesignLinter, TokenCandidate, TokensFile } from '../design-linter';

export class TokenWizard {
    private linter: DesignLinter;
    private workspaceRoot: string;

    constructor(linter: DesignLinter, workspaceRoot: string) {
        this.linter = linter;
        this.workspaceRoot = workspaceRoot;
    }

    public async run(): Promise<void> {
        try {
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "AI-Stethic: Initializing Design Tokens",
                cancellable: false
            }, async (progress) => {
                progress.report({ increment: 0, message: "Starting token wizard..." });

                // Step 1: Choose initialization method
                const initMethod = await this.chooseInitializationMethod();
                if (!initMethod) return;

                progress.report({ increment: 20, message: "Scanning workspace..." });

                let tokensFile: TokensFile | undefined;

                if (initMethod === 'import') {
                    tokensFile = await this.importExistingTokens();
                } else if (initMethod === 'scan') {
                    tokensFile = await this.scanAndCreateTokens(progress);
                } else {
                    tokensFile = this.createEmptyTokensFile();
                }

                if (!tokensFile) return;

                progress.report({ increment: 80, message: "Saving tokens file..." });

                // Save tokens file
                const config = vscode.workspace.getConfiguration('aiStethic');
                const tokensFilePath = path.join(
                    this.workspaceRoot, 
                    config.get('tokensFile', '.ai-stethic/tokens.json')
                );

                this.linter.saveTokensFile(tokensFile, tokensFilePath);

                progress.report({ increment: 100, message: "Complete!" });

                vscode.window.showInformationMessage(
                    `AI-Stethic: Design tokens initialized successfully! Found ${tokensFile.colors.length} colors, ${tokensFile.spacing.length} spacing values, ${tokensFile.typography.length} typography tokens, and ${tokensFile.borderRadius.length} border-radius values.`
                );
            });
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to initialize tokens: ${error}`);
        }
    }

    private async chooseInitializationMethod(): Promise<string | undefined> {
        const options = [
            {
                label: '🔍 Scan Codebase',
                description: 'Automatically detect tokens from existing CSS/JSX files',
                value: 'scan'
            },
            {
                label: '📁 Import JSON',
                description: 'Import from an existing tokens.json file',
                value: 'import'
            },
            {
                label: '📝 Start Blank',
                description: 'Create an empty tokens file',
                value: 'blank'
            }
        ];

        const selection = await vscode.window.showQuickPick(options, {
            placeHolder: 'How would you like to initialize your design tokens?'
        });

        return selection?.value;
    }

    private async importExistingTokens(): Promise<TokensFile | undefined> {
        const fileUri = await vscode.window.showOpenDialog({
            canSelectFiles: true,
            canSelectFolders: false,
            canSelectMany: false,
            filters: {
                'JSON files': ['json']
            },
            openLabel: 'Import Tokens'
        });

        if (!fileUri || fileUri.length === 0) return undefined;

        try {
            const fs = require('fs');
            const content = fs.readFileSync(fileUri[0].fsPath, 'utf-8');
            const imported = JSON.parse(content);

            // Validate structure
            if (!this.validateTokensFileStructure(imported)) {
                vscode.window.showErrorMessage('Invalid tokens file structure. Please ensure it contains colors, spacing, typography, borderRadius, and components sections.');
                return undefined;
            }

            return imported as TokensFile;
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to import tokens file: ${error}`);
            return undefined;
        }
    }

    private async scanAndCreateTokens(progress: vscode.Progress<{ increment?: number; message?: string }>): Promise<TokensFile | undefined> {
        try {
            // Scan workspace for token candidates
            const candidates = await this.linter.scanWorkspaceForTokens(this.workspaceRoot);
            
            progress.report({ increment: 40, message: "Processing color tokens..." });

            // Process colors
            const selectedColors = await this.selectTokenCandidates(
                candidates.colors,
                'color',
                'Select colors to include in your design system:'
            );

            progress.report({ increment: 50, message: "Processing spacing tokens..." });

            // Process spacing
            const selectedSpacing = await this.selectTokenCandidates(
                candidates.spacing,
                'spacing',
                'Select spacing values to include:'
            );

            progress.report({ increment: 60, message: "Processing typography tokens..." });

            // Process typography
            const selectedTypography = await this.selectTokenCandidates(
                candidates.typography,
                'typography',
                'Select typography tokens to include:'
            );

            progress.report({ increment: 70, message: "Processing border-radius tokens..." });

            // Process border-radius
            const selectedBorderRadius = await this.selectTokenCandidates(
                candidates.borderRadius,
                'borderRadius',
                'Select border-radius values to include:'
            );

            // Create tokens file
            const tokensFile = this.linter.createTokensFile({
                colors: selectedColors,
                spacing: selectedSpacing,
                typography: selectedTypography,
                borderRadius: selectedBorderRadius
            });

            return tokensFile;
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to scan workspace: ${error}`);
            return undefined;
        }
    }

    private async selectTokenCandidates(
        candidates: TokenCandidate[],
        type: string,
        prompt: string
    ): Promise<TokenCandidate[]> {
        if (candidates.length === 0) {
            return [];
        }

        const items = candidates.map(candidate => ({
            label: candidate.suggestedName || candidate.value,
            description: candidate.value,
            detail: `Found in ${candidate.occurrences.length} location(s)`,
            candidate
        }));

        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: prompt,
            canPickMany: true,
            ignoreFocusOut: true
        });

        if (!selected) return [];

        // Allow users to rename tokens
        const finalCandidates: TokenCandidate[] = [];
        for (const item of selected) {
            const name = await vscode.window.showInputBox({
                prompt: `Name for ${type} token`,
                value: item.candidate.suggestedName || item.candidate.value,
                placeHolder: 'Enter token name'
            });

            if (name) {
                finalCandidates.push({
                    ...item.candidate,
                    suggestedName: name
                });
            }
        }

        return finalCandidates;
    }

    private createEmptyTokensFile(): TokensFile {
        return {
            colors: [],
            spacing: [],
            typography: [],
            borderRadius: [],
            components: {}
        };
    }

    private validateTokensFileStructure(obj: any): boolean {
        return obj &&
               Array.isArray(obj.colors) &&
               Array.isArray(obj.spacing) &&
               Array.isArray(obj.typography) &&
               Array.isArray(obj.borderRadius) &&
               typeof obj.components === 'object';
    }
}
