{"name": "ai-stethic", "displayName": "AI-Stethic", "description": "Design system consistency enforcement for React projects", "version": "0.1.0", "publisher": "ai-stethic", "engines": {"vscode": "^1.74.0"}, "categories": ["Linters", "Other"], "keywords": ["design-system", "tokens", "css", "react", "linting"], "activationEvents": ["onLanguage:css", "onLanguage:scss", "onLanguage:javascriptreact", "onLanguage:typescriptreact"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "aiStethic.initializeTokens", "title": "AI-Stethic: Initialize Design Tokens"}, {"command": "aiStethic.registerComponents", "title": "AI-Stethic: Register Components"}, {"command": "aiStethic.enhanceComponent", "title": "AI-Stethic: <PERSON><PERSON><PERSON>mpo<PERSON>"}, {"command": "aiStethic.addToken", "title": "AI-Stethic: <PERSON><PERSON>"}], "configuration": {"title": "AI-Stethic", "properties": {"aiStethic.tokensFile": {"type": "string", "default": ".ai-stethic/tokens.json", "description": "Path to the design tokens file"}, "aiStethic.enableLinter": {"type": "boolean", "default": true, "description": "Enable design system linting"}, "aiStethic.enableTokenLinting": {"type": "boolean", "default": true, "description": "Enable token-level linting"}, "aiStethic.enableComponentLinting": {"type": "boolean", "default": true, "description": "Enable component-level linting"}, "aiStethic.colorThreshold": {"type": "number", "default": 10, "description": "Color similarity threshold for matching"}, "aiStethic.spacingThreshold": {"type": "number", "default": 2, "description": "Spacing similarity threshold for matching (in pixels)"}, "aiStethic.componentDirectory": {"type": "string", "default": "src/components/ui", "description": "Directory to scan for React components"}, "aiStethic.enhanceApiUrl": {"type": "string", "default": "http://localhost:8000/enhance", "description": "URL for the enhance component API"}}}, "languages": [{"id": "css", "extensions": [".css"]}, {"id": "scss", "extensions": [".scss"]}, {"id": "javascriptreact", "extensions": [".jsx"]}, {"id": "typescriptreact", "extensions": [".tsx"]}]}, "scripts": {"build": "tsc", "watch": "tsc --watch", "package": "vsce package", "clean": "rm -rf dist"}, "dependencies": {"axios": "^1.6.0", "postcss": "^8.4.32", "postcss-safe-parser": "^7.0.0", "tinycolor2": "^1.6.0"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "^20.0.0", "typescript": "^5.3.0", "@vscode/vsce": "^2.22.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/ai-stethic-mvp.git"}, "bugs": {"url": "https://github.com/your-org/ai-stethic-mvp/issues"}, "homepage": "https://github.com/your-org/ai-stethic-mvp#readme", "license": "MIT"}